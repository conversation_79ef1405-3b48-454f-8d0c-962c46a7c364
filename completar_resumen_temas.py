#!/usr/bin/env python3
"""
Script para completar el archivo Resumen_Semi_Automatico.md con los temas 8-21
Extrae contenido de PDFs específicos y actualiza el resumen académico
"""

import os
import sys
import logging
from datetime import datetime
from pathlib import Path
import time
import traceback

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/completar_resumen.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class ResumenCompleter:
    """Clase para completar el resumen académico con contenido de PDFs"""
    
    def __init__(self):
        self.presentaciones_dir = Path("Presentaciones")
        self.resumen_file = Path("Resumen_Semi_Automatico.md")
        
        # Mapeo de temas según el glosario
        self.temas_mapping = {
            9: {
                'title': 'ITGC y Gobierno TI',
                'path': 'Presentaciones/UBA FCE - Seg Inf y Ppios Aud - ITGC_Gobierno TI.pdf'
            },
            10: {
                'title': 'Cyber Threat Intelligence y SOC Radar',
                'path': 'Presentaciones/UBA FCE - Seg Inf y Ppios Aud - SOC radar.pdf'
            },
            11: {
                'title': 'Gestión de riesgos ERM',
                'path': 'Presentaciones/UBA FCE - Seg Inf y Ppios Aud - Gestión de riesgos ERM.pdf'
            },
            12: {
                'title': 'COSO',
                'path': 'Presentaciones/UBA FCE - Seg Inf y Ppios Aud - COSO.pdf'
            },
            13: {
                'title': 'BCP',
                'path': 'Presentaciones/UBA FCE - Seg Inf y Ppios Aud - BCP.pdf'
            },
            14: {
                'title': 'ITGC en detalle',
                'path': 'Presentaciones/UBA FCE - Seg Inf y Ppios Aud - ITGC.pdf'
            },
            15: {
                'title': 'Procesos de Negocio',
                'path': 'Presentaciones/UBA FCE - Seg Inf y Ppios Aud - Procesos de Negocio.pdf'
            },
            16: {
                'title': 'Normas Auditoría',
                'path': 'Presentaciones/UBA FCE - Seg Inf y Ppios Aud - Normas Auditoría.pdf'
            },
            17: {
                'title': 'Planificación, Ejecución y Pruebas de Auditoría',
                'path': 'Presentaciones/UBA FCE - Seg Inf y Ppios Aud -Planificacion_Ejecución_Pruebas de Auditoria.pdf'
            },
            18: {
                'title': 'Seguridad en el SDLC y DevSecOps',
                'path': 'Presentaciones/UBA FCE - Auditoria y Seguridad - Seguridad en el SDLC y DevSecOps.pptx.pdf'
            },
            19: {
                'title': 'Tipo de amenazas y ataques. Técnicas de explotación',
                'path': 'Presentaciones/UBA FCE - Auditoria y Seguridad - Tipo de amenazas y ataques. Técnicas de explotación.pptx.pdf'
            },
            20: {
                'title': 'Detección y respuesta a incidentes de Seguridad',
                'path': 'Presentaciones/UBA FCE - Auditoria y Seguridad - Detección y respuesta a incidentes de Seguridad.pptx.pdf'
            },
            21: {
                'title': 'Pruebas de Penetración & Auditoría de código',
                'path': 'Presentaciones/UBA FCE - Auditoria y Seguridad - Pruebas de Penetracion & Auditoria de codigo.pdf'
            }
        }
        
        self.check_dependencies()
    
    def check_dependencies(self):
        """Verificar dependencias necesarias"""
        try:
            import pdfplumber
            import PyPDF2
        except ImportError as e:
            logger.error(f"Dependencias faltantes: {e}")
            logger.info("Instalando dependencias...")
            import subprocess
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pdfplumber", "PyPDF2"])
    
    def extract_pdf_content(self, file_path):
        """Extraer contenido de un archivo PDF"""
        try:
            import pdfplumber
            
            logger.info(f"Extrayendo contenido de: {file_path.name}")
            
            content_text = []
            
            with pdfplumber.open(file_path) as pdf:
                for page_num, page in enumerate(pdf.pages, 1):
                    text = page.extract_text()
                    if text and text.strip():
                        content_text.append(text.strip())
            
            return "\n\n".join(content_text)
            
        except Exception as e:
            logger.error(f"Error extrayendo contenido de {file_path}: {e}")
            return ""
    
    def process_content_for_tema(self, tema_num, raw_content):
        """Procesar y estructurar el contenido extraído para un tema específico"""
        if not raw_content:
            return ""
        
        # Limpiar y estructurar el contenido
        lines = raw_content.split('\n')
        processed_lines = []
        
        for line in lines:
            line = line.strip()
            if line and len(line) > 3:  # Filtrar líneas muy cortas
                processed_lines.append(line)
        
        # Estructurar según el tema
        tema_info = self.temas_mapping[tema_num]
        
        structured_content = f"""
## {tema_num}. {tema_info['title']}

### Conceptos Principales

{chr(10).join(f"* {line}" for line in processed_lines[:10] if line)}

### Puntos Clave

{chr(10).join(f"* {line}" for line in processed_lines[10:20] if line)}

### Información Adicional

{chr(10).join(processed_lines[20:] if len(processed_lines) > 20 else processed_lines[10:])}

### Relación con otros temas
*   **Conexión con el marco general:** Este tema se integra con los conceptos de seguridad y auditoría del curso.

---
"""
        
        return structured_content
    
    def read_current_resumen(self):
        """Leer el contenido actual del archivo de resumen"""
        try:
            with open(self.resumen_file, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            logger.error(f"Error leyendo archivo de resumen: {e}")
            return ""
    
    def find_insertion_point(self, current_content):
        """Encontrar el punto donde insertar el nuevo contenido"""
        # Buscar el final del tema 8 o el inicio de la conclusión
        lines = current_content.split('\n')
        
        for i, line in enumerate(lines):
            if "## Conclusión General" in line or "## Información Adicional" in line:
                return i
        
        # Si no encuentra, insertar antes de las últimas líneas
        return len(lines) - 10
    
    def complete_resumen(self):
        """Completar el archivo de resumen con los temas faltantes"""
        logger.info("Iniciando completado del resumen académico...")
        
        # Leer contenido actual
        current_content = self.read_current_resumen()
        if not current_content:
            logger.error("No se pudo leer el archivo de resumen actual")
            return False
        
        # Extraer contenido de cada tema
        new_sections = []
        
        for tema_num in range(9, 22):  # Temas 9-21
            if tema_num in self.temas_mapping:
                tema_info = self.temas_mapping[tema_num]
                file_path = Path(tema_info['path'])
                
                if file_path.exists():
                    logger.info(f"Procesando Tema {tema_num}: {tema_info['title']}")
                    
                    # Extraer contenido del PDF
                    raw_content = self.extract_pdf_content(file_path)
                    
                    if raw_content:
                        # Procesar y estructurar el contenido
                        structured_content = self.process_content_for_tema(tema_num, raw_content)
                        new_sections.append(structured_content)
                        logger.info(f"✅ Tema {tema_num} procesado exitosamente")
                    else:
                        logger.warning(f"❌ No se pudo extraer contenido del Tema {tema_num}")
                else:
                    logger.warning(f"❌ Archivo no encontrado para Tema {tema_num}: {file_path}")
        
        if not new_sections:
            logger.error("No se pudo extraer contenido de ningún tema")
            return False
        
        # Encontrar punto de inserción
        insertion_point = self.find_insertion_point(current_content)
        
        # Insertar nuevo contenido
        lines = current_content.split('\n')
        new_content_lines = lines[:insertion_point]
        
        # Agregar las nuevas secciones
        for section in new_sections:
            new_content_lines.extend(section.split('\n'))
        
        # Agregar el resto del contenido original
        new_content_lines.extend(lines[insertion_point:])
        
        # Crear backup del archivo original
        backup_path = self.resumen_file.with_suffix('.md.backup')
        with open(backup_path, 'w', encoding='utf-8') as f:
            f.write(current_content)
        
        logger.info(f"Backup creado en: {backup_path}")
        
        # Escribir el nuevo contenido
        new_content = '\n'.join(new_content_lines)
        
        with open(self.resumen_file, 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        logger.info(f"✅ Resumen completado exitosamente")
        logger.info(f"Se agregaron {len(new_sections)} nuevas secciones")
        
        return True

def main():
    """Función principal"""
    print("🚀 COMPLETADOR DE RESUMEN ACADÉMICO")
    print("=" * 60)
    print("Extrayendo contenido de PDFs para temas 9-21...")
    print()
    
    try:
        # Crear directorio de logs si no existe
        Path("logs").mkdir(exist_ok=True)
        
        # Crear instancia del completador
        completer = ResumenCompleter()
        
        # Completar el resumen
        success = completer.complete_resumen()
        
        if success:
            print("\n✅ Resumen completado exitosamente!")
            print("📄 Archivo actualizado: Resumen_Semi_Automatico.md")
            print("💾 Backup creado: Resumen_Semi_Automatico.md.backup")
        else:
            print("\n❌ Error completando el resumen")
        
    except KeyboardInterrupt:
        print("\n⚠️ Proceso interrumpido por el usuario")
    except Exception as e:
        print(f"\n❌ Error crítico: {e}")
        logger.error(f"Error crítico: {e}")
        logger.error(traceback.format_exc())

if __name__ == "__main__":
    main()
