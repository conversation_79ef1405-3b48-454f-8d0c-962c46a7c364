<PERSON><PERSON><PERSON>, aquí tienes un resumen completo y ordenado de la información proporcionada, listo para ser estudiado y en formato Markdown.

---

# Resumen de Seguridad de la Información y Principios de Auditoría (UBA FCE)

Este documento resume los temas clave de la materia, estructurados según el glosario provisto, y conectando los conceptos entre sí para una comprensión integral.

## 8. Awareness (Concienciación en Seguridad)

La concienciación es la primera línea de defensa, enfocada en el factor humano, que a menudo es el eslabón más débil en la cadena de seguridad.

### ¿Por qué es Fundamental?
*   El **factor humano** es el vector de ataque inicial más exitoso. La falta de conocimiento o el descuido de un usuario pueden anular defensas tecnológicas robustas.
*   Casos como **LAPSUS$** o el incidente de **Mercado Libre** demuestran que los ataques a menudo explotan la ingeniería social y el robo de credenciales, no necesariamente vulnerabilidades técnicas complejas.
*   **Evidencia práctica:** Sin importar cuántos firewalls, sistemas de detección de intrusiones o herramientas de seguridad sofisticadas se implementen, el factor humano sigue siendo, en la gran mayoría de los casos, el vector de ataque inicial más exitoso.
*   **Realidad actual:** Los grupos criminales cibernéticos logran vulnerar defensas robustas no atacando directamente los sistemas con exploits complejos de día cero, sino aprovechando la ingeniería social, el robo de credenciales o el acceso a través de contratistas.

### Vectores de Ataque Comunes que Explotan al Usuario
*   **Phishing:** Suplantación de identidad (generalmente por email) para engañar a la víctima y que entregue información confidencial (credenciales, datos de tarjetas). Los emails parecen provenir de bancos, servicios de streaming o incluso colegas, dirigiendo a sitios web falsos.
*   **Malware:** Software malicioso (virus, gusanos, troyanos) diseñado para dañar sistemas, robar información o tomar control. Incluye ransomware que cifra datos y exige rescate.
*   **Ingeniería Social:** El arte de manipular a las personas para que realicen acciones o divulguen información confidencial. No siempre involucra tecnología; a veces es solo una llamada telefónica o conversación casual. Es la base de muchos ataques de phishing y malware.
*   **Robo de Identidad:** Asumir la identidad de otra persona para acceder a sus cuentas o realizar acciones maliciosas en su nombre.
*   **Fuga de Datos (Data Breach):** Exposición o robo de información confidencial.
*   **Inteligencia Cibernética:** Recopilación de información sobre un objetivo, la organización o sus empleados, para planificar futuros ataques.

> **Nota importante:** Muchos ataques de malware o ransomware en realidad comienzan con un ataque de ingeniería social dirigido a un empleado. La diversidad de vectores de ataque demuestra por qué la concienciación es tan crítica.

### ¿Cómo Construir un Programa de Awareness Efectivo?
1.  **Comunicación Constante y Creativa:** No basta con comunicados ocasionales. Se deben usar campañas regulares a través de:
    *   Protectores de pantalla en computadoras de oficina
    *   Posters en áreas comunes
    *   Emails internos con mensajes de seguridad periódicos
    *   La idea es mantener la seguridad en la mente de los empleados constantemente

2.  **Ejercicios Prácticos:** Realizar **simulacros de phishing** para medir la reacción de los empleados e identificar áreas de mejora.
    *   Se envían emails simulados que imitan ataques reales para ver cuántos empleados hacen clic en enlaces maliciosos o descargan archivos sospechosos
    *   **El objetivo es educar, no castigar** - es una forma de prueba y aprendizaje
    *   Ayuda a identificar vulnerabilidades en el entrenamiento y áreas donde el mensaje necesita refuerzo

3.  **Capacitación Formal:** Sesiones (online o presenciales) que expliquen los riesgos, las políticas de seguridad y cómo identificar y reportar incidentes.

4.  **Apoyo de la Dirección:** El compromiso de los líderes es crucial para que el personal se tome la seguridad en serio. Si la alta gerencia no muestra compromiso con la seguridad, es muy difícil que el resto del personal la tome en serio. El mensaje de seguridad debe venir de arriba hacia abajo.

5.  **Medición de Impacto:** Evaluar métricas como:
    *   ¿Están disminuyendo las tasas de clics en ejercicios de phishing?
    *   ¿La gente está reportando más emails sospechosos?
    *   ¿Hay menos incidentes relacionados con errores humanos?
    *   Estas métricas ayudan a justificar la inversión en el programa de concienciación y dirigir esfuerzos futuros donde más se necesiten.

### Relación con otros temas
*   **Gestión de Riesgos (ERM):** Reduce directamente el riesgo de que amenazas como el phishing se materialicen.
*   **COSO:** Fortalece el **Ambiente de Control**, uno de los componentes clave del marco.
*   **Amenazas y Ataques:** Es la defensa humana directa contra las técnicas de ingeniería social y phishing.

---

## 9. Gobierno de TI (IT Governance)

El Gobierno de TI es el marco de liderazgo y estructura organizacional que garantiza que la tecnología de la información (TI) respalde y potencie los objetivos estratégicos del negocio.

### Definición y Propósito
*   **Definición:** Conjunto de estructuras, procesos y mecanismos para dirigir y controlar los recursos de TI de forma segura y eficiente.
*   **Propósito:** Asegurar que las inversiones y el uso de la TI creen valor para los stakeholders, optimizando costos y gestionando los riesgos tecnológicos inherentes.

### Diferencia Clave: Gobierno vs. Gestión
Esta es una distinción conceptual muy importante que a menudo se confunde:

*   **Gobierno de TI (Dirección y Supervisión):** Define el **qué** y el **por qué**.
    *   Evalúa las necesidades estratégicas y opciones de los stakeholders
    *   Establece la dirección que debe tomar la función de TI
    *   Toma decisiones sobre prioridades, inversiones, políticas y marcos de control
    *   Monitorea el desempeño de TI, cumplimiento de políticas y logro de objetivos
    *   Es la función de definir qué hacer y por qué, y supervisar que se haga correctamente

*   **Gestión de TI (Ejecución):** Define el **cómo** y el **cuándo**.
    *   Es la función ejecutiva que planifica, construye, opera y monitorea las actividades operativas diarias, semanales o mensuales de la función de TI
    *   Siempre opera bajo la dirección y dentro de las políticas y marcos establecidos por el gobierno de TI
    *   Es la ejecución operacional de la estrategia definida por el gobierno

> **Analogía:** El Gobierno de TI es como el timón que dirige el barco tecnológico de la organización en la dirección correcta y evita las rocas.

### Pilares del Gobierno de TI
1.  **Alineamiento Estratégico:** Asegurar que la TI apoya las metas del negocio.
2.  **Roles y Responsabilidades:** Definir quién toma las decisiones de TI y quién es responsable de qué.
3.  **Gestión de Riesgos:** Identificar, evaluar y tratar los riesgos relacionados con TI.
4.  **Gestión de Recursos:** Optimizar el uso de personas, hardware, software e información.
5.  **Cumplimiento:** Garantizar el acatamiento de leyes, regulaciones y políticas internas.

### Marcos de Referencia
Para implementar este gobierno efectivamente, existen marcos y mejores prácticas útiles:

*   **COBIT (Control Objectives for Information and related Technology):** Es el marco más completo y destacado como modelo de referencia muy comprensivo.
    *   Proporciona un conjunto detallado de procesos clave de TI (34 en su versión anterior, el marco ha evolucionado)
    *   Cubre todo desde planificación y organización hasta entrega y soporte, monitoreo y evaluación
    *   Ayuda a las organizaciones a implementar gobierno de TI estructurando sus procesos y controles

*   **ISO/IEC 38500:** Norma internacional para el gobierno de TI.
*   **AS8-015-2005:** Estándar australiano similar.

### Relación con otros temas
*   **ITGC y Controles de Proceso:** El Gobierno de TI establece las políticas y la estructura que los ITGC y otros controles deben hacer cumplir.
*   **Gestión de Riesgos (ERM):** La gestión de riesgos de TI es un pilar fundamental del Gobierno de TI.
*   **COSO:** El Gobierno de TI es un componente esencial del gobierno corporativo general y del **Ambiente de Control**.

---

## 10 y 14. Controles Generales de TI (ITGC - IT General Controls)

Los ITGC son la base del control tecnológico. No están ligados a una aplicación específica, sino que se aplican a todo el entorno tecnológico (sistemas, redes, aplicaciones).

### ¿Por qué son tan Relevantes para la Auditoría?
Los ITGC crean y soportan el ambiente donde ocurren cosas críticas:

*   **Son la Base Tecnológica:** Si los ITGC son débiles, no se puede confiar en la información generada por los sistemas ni en los controles automáticos que operan dentro de ellos.
    *   Se generan y procesan cálculos importantes
    *   Se producen reportes, muchos de los cuales pueden ser información financiera
    *   Operan controles automatizados dentro de aplicaciones
    *   Se gestiona la seguridad lógica, incluyendo segregación de funciones (SOD)
    *   Funcionan interfaces entre diferentes sistemas

*   **Impacto en Estados Financieros:** Afectan directamente la fiabilidad de la información financiera.
    *   Los ITGC robustos son fundamentales para soportar la confiabilidad del ambiente de TI que impacta la validez de casi todas las aserciones de estados financieros (exactitud, integridad, corte, existencia y ocurrencia, derechos y obligaciones, presentación y revelación)
    *   Un auditor debe evaluarlos para determinar cuánta confianza puede depositar en los sistemas para soportar saldos financieros y operaciones

*   **Eficiencia de la Auditoría:** Si los ITGC son fuertes, el auditor puede reducir la cantidad de pruebas sustantivas (manuales), que son más costosas y consumen más tiempo. Si los ITGC son débiles, el auditor necesitará realizar muchas más pruebas sustantivas manuales.

### Los 4 Dominios Principales de ITGC

#### 1. Acceso a Programas y Datos
*   **Objetivo:** Asegurar que solo las personas autorizadas tengan acceso a los sistemas y datos, y solo con los permisos necesarios para su función (**principio de mínimo privilegio**).
*   **Riesgos Principales:**
    *   Acceso no autorizado a aplicaciones por usuarios de negocio (personal interno)
    *   Acceso no autorizado a aplicaciones y datos por usuarios de TI (personal técnico que gestiona los sistemas)
    *   Cambios no autorizados en los datos

*   **Capas de Seguridad:** La seguridad de información y administración de accesos se gestiona y monitorea a través de múltiples capas:
    *   Seguridad en el perímetro de red (frontera con el mundo exterior)
    *   Red interna
    *   Sistema operativo de servidores
    *   Los datos mismos (bases de datos, archivos, etc.)
    *   Aplicaciones de negocio

*   **Controles Clave:**
    *   Proceso formal de alta, baja y modificación de usuarios (ABM de usuarios), con solicitudes y autorizaciones documentadas
    *   Revisión periódica de los accesos concedidos para asegurar que sigan siendo apropiados
    *   Controles estrictos sobre cuentas privilegiadas (administradores, superusuarios) y genéricas
    *   Implementación de controles de **Segregación de Funciones (SoD)** para evitar conflictos de roles
    *   Control riguroso sobre cuentas privilegiadas o genéricas dentro de aplicaciones

### Controles Específicos por Tipo de Seguridad

#### Seguridad de Aplicaciones (Para usuarios de negocio y superusuarios)
**Controles Preventivos:**
*   Diseño apropiado de roles y perfiles de usuario con permisos mínimos necesarios (principio de menor privilegio)
*   Proceso formal y documentado de aprovisionamiento de usuarios donde el acceso se solicita, autoriza y otorga de manera controlada
*   Implementación de controles compensatorios cuando no se pueden evitar conflictos de SOD
*   Restricción y control estricto del acceso para terceros (consultores o proveedores)

**Controles Detectivos:**
*   Análisis periódico de roles y permisos asignados a usuarios para identificar conflictos de SOD existentes
*   Procesos para identificar y remediar estos conflictos una vez detectados
*   Monitoreo de la actividad de usuarios con permisos elevados o funciones críticas dentro de la aplicación

#### Seguridad de Datos (Para personal técnico como DBAs)
**Controles Clave:**
*   Todas las solicitudes de acceso a bases de datos o archivos sensibles deben ser apropiadamente revisadas y autorizadas
*   Proceso eficiente y oportuno para remover acceso de personal que ha dejado la organización
*   Monitoreo periódico de transacciones y actividades realizadas por superusuarios o administradores de base de datos para detectar actividades sospechosas o no autorizadas
*   Aplicación de estándares robustos de seguridad de base de datos y políticas de contraseñas fuertes

#### Seguridad del Sistema Operativo
**Controles Fundamentales:**
*   Restricción del acceso a cuentas privilegiadas del SO (como administrador en Windows o Root en sistemas Unix) solo al personal técnico estrictamente necesario
*   Monitoreo periódico de la actividad de estos superusuarios o administradores del SO para identificar acciones inusuales
*   Aplicación de estándares robustos de seguridad y políticas de contraseñas fuertes a los sistemas operativos mismos

#### Administración de Accesos Diferenciada

**Para Usuarios de Negocio:**
*   El acceso debe ser formalmente solicitado por personal autorizado (usualmente un gerente de área de negocio) y otorgado por una función separada dentro de TI
*   Revisiones periódicas del acceso otorgado para asegurar que sigue siendo apropiado y necesario
*   Remoción de acceso para personal que se va debe ser oportuna, idealmente coordinada con el proceso de desvinculación del empleado
*   Cualquier incidente de seguridad relacionado con acceso debe ser identificado e investigado

**Para Usuarios Técnicos:**
*   Casi todos los componentes de TI tienen cuentas administrativas privilegiadas (administrador de dominio Windows, Root Unix, QSECOFR en OS 400, DBA en SQL Server u Oracle, administradores de firewall, etc.)
*   Debido al potencial de abuso o error con estas cuentas, se necesitan medidas de seguridad adicionales
*   **Gestión de Contraseñas Privilegiadas:** Tradicionalmente se usaba "password vaulting" (almacenar la contraseña maestra en un sobre sellado accedido solo en emergencias), aunque hoy esto se gestiona más efectivamente con software especializado de gestión de acceso privilegiado (PAM)
*   La asignación de acceso a estas cuentas debe ser específica y basada en roles, evitando credenciales compartidas

#### 2. Cambios a Programas (y Desarrollo)
*   **Objetivo:** Garantizar que solo los cambios autorizados, probados y correctos se implementen en el entorno de producción.
*   **Riesgos Específicos:**
    *   Implementar cambios que no fueron solicitados por el negocio o que introducen errores en aplicaciones
    *   Hacer cambios directos no autorizados a programas en producción o su configuración
    *   Aplicar actualizaciones inadecuadas del sistema operativo o base de datos que afecten estabilidad o seguridad
    *   Implementar nuevos sistemas o módulos incorrectamente debido a errores de codificación, errores de configuración, o incluir desarrollos no autorizados
    *   Errores durante migración o conversión de datos (transaccionales o maestros) como parte de un cambio o implementación

*   **Separación de Ambientes (Fundamental):**
    *   **Desarrollo (DEV):** Donde los programadores escriben y prueban inicialmente el código
    *   **Pruebas/QA:** Donde los cambios se someten a pruebas más rigurosas (funcionales, de rendimiento, seguridad), a menudo por personal diferente a los desarrolladores
    *   **Producción (PROD):** Lo que usan los usuarios de negocio para sus operaciones diarias
    *   Esta separación (física o lógica) es vital para prevenir que desarrolladores trabajen directamente en producción

*   **Controles Clave:**
    *   Proceso formal de gestión de cambios: solicitud, análisis, diseño, pruebas (incluyendo pruebas de aceptación del usuario - UAT) y aprobación formal antes del pase a producción
    *   Segregación de funciones: El personal de desarrollo no debe tener permisos para mover cambios a producción
    *   Verificar que cada cambio fue formalmente solicitado y cumple especificaciones
    *   Verificar que se generó un documento de diseño funcional aprobado
    *   Verificar que el cambio fue probado exhaustivamente en el ambiente de pruebas/QA
    *   Verificar que el cambio tiene la autorización formal requerida antes de moverse al ambiente de producción

#### 3. Operaciones de Cómputo
*   **Objetivo:** Asegurar que los sistemas que soportan procesos clave de negocio funcionen correcta y continuamente. Son responsables de asegurar que los datos de producción de la empresa se almacenen, preserven y transfieran con integridad y exactitud.
*   **Riesgos:** Interrupción de los servicios, pérdida de datos, procesamiento incorrecto de transacciones.
*   **Tres Áreas Principales:**
    1. **Procesamiento Automatizado de Información:** Actividades de procesamiento de transacciones que el sistema realiza automáticamente
    2. **Gestión y Resolución de Problemas:** Cómo se identifican, gestionan y corrigen fallas o errores
    3. **Operaciones del Centro de Datos:** Actividades relacionadas con la infraestructura física y lógica del centro de datos

*   **Controles Clave por Área:**

    **Procesamiento de Información:**
    *   Programación y monitoreo de procesos batch (como cálculo de nómina o procesamiento nocturno de transacciones)
    *   Procesamiento en tiempo real (transacciones procesadas inmediatamente, como una transacción de punto de venta)
    *   Procesamiento de interfaces (transferencia automatizada de datos entre diferentes sistemas)
    *   Monitoreo general del procesamiento de transacciones para detectar anomalías o errores

    **Gestión de Problemas:**
    *   Identificación de problemas o incidentes técnicos
    *   Monitoreo de redes y sistemas para detectar fallas de rendimiento o disponibilidad
    *   Funciones de Help Desk/Service Desk (primer punto de contacto para usuarios que reportan problemas)
    *   Escalamiento basado en niveles (Nivel 1 para problemas básicos, Nivel 2 y 3 para problemas técnicos más complejos que requieren especialistas)

    **Operaciones del Centro de Datos:**
    *   Gestión de backups y proceso de recuperación de información, restauración de datos
    *   Existencia e implementación de plan de recuperación ante desastres (DRP) y plan de continuidad del negocio (BCP)
    *   Protección ambiental del centro de datos (control de temperatura y humedad, sistemas de supresión de incendios, seguridad física)
    *   Gestión de capacidad para asegurar que la infraestructura de TI tenga recursos suficientes para soportar operaciones actuales y futuras del negocio

### Controles Específicos por Tipo de Actividad Operacional

#### Controles Gerenciales
*   Tener políticas, estándares y procedimientos claros para actividades de monitoreo y operacionales
*   Definir roles y responsabilidades claros, donde nuevamente la segregación de funciones y gestión crítica de accesos son relevantes
*   Establecer controles generales de monitoreo sobre infraestructura y procesos

#### Controles para Procesamiento Batch
*   Procesos definidos para agregar, remover y modificar trabajos batch
*   Procedimientos de recuperación probados para cada trabajo en caso de interrupción
*   Monitoreo constante de la ejecución de estos procesos batch para asegurar que se completen correcta y oportunamente
*   Controles robustos de acceso sobre la herramienta que gestiona y monitorea estos trabajos

#### Controles para Procesamiento en Tiempo Real
*   Los aspectos de configuración deben ser controlados, especialmente para transacciones complejas o aquellas que usan middleware
*   Es crucial definir cómo se capturan los errores que ocurren durante el procesamiento y cómo se generan alertas para corrección oportuna
*   Controles de acceso para quién puede modificar la configuración de estas transacciones críticas en tiempo real

#### Controles para Backups y Gestión de Problemas
*   Debe asegurarse que el contenido de los backups y su frecuencia estén alineados con los objetivos de continuidad del negocio, específicamente el RPO (Recovery Point Objective - la pérdida máxima tolerable de datos)
*   La empresa necesita certeza de que los backups estarán disponibles y accesibles cuando se necesiten
*   Debe tener un proceso documentado y, crucialmente, **probado** para restaurar o recuperar información de esos backups

> **Nota crítica:** Este dominio de operaciones es la columna vertebral tecnológica que mantiene todo funcionando. Si falla, el negocio se detiene.

### Relación con otros temas
*   **COSO:** Son la materialización de las **Actividades de Control** en el dominio de TI.
*   **BCP/DRP:** El dominio de Operaciones (backups, DRP) es el soporte técnico del BCP.
*   **Procesos de Negocio:** Unos ITGC sólidos son necesarios para poder confiar en los controles de aplicación.

---

## 11. Gestión de Riesgos (ERM - Enterprise Risk Management)

ERM es un proceso integral para identificar, evaluar y responder a los riesgos potenciales que podrían afectar el logro de los objetivos de una organización.

### Conceptos Fundamentales
*   **Riesgo:** Posibilidad de que ocurra un evento con un impacto negativo en los objetivos. Se mide por **probabilidad** e **impacto**.
*   **Marco de Referencia:** **COSO ERM** es el modelo principal. Su versión de 2017 ("Integrando con Estrategia y Desempeño") enfatiza la gestión de riesgos como una herramienta para crear y preservar valor.

### Conceptos Clave para la Toma de Decisiones
Estos son conceptos clave que guían la toma de decisiones relacionadas con riesgos:

*   **Apetito de Riesgo:** La cantidad de riesgo que una organización está dispuesta a aceptar de forma estratégica para alcanzar sus objetivos. Definir el apetito de riesgo ayuda a alinear la estrategia, estructura organizacional y procesos.
    *   **Factores a considerar:** Modelo de negocio, perfil de riesgo actual de la organización, su cultura, riesgos inherentes de su industria, necesidad de equilibrar riesgo con oportunidad de retornos
    *   **Categorías de apetito:**
        - **Intolerante:** Muy cauteloso, baja disposición a la incertidumbre
        - **Adverso:** Moderado, acepta riesgos justificados y monitoreables
        - **Tolerante:** Predispuesto al riesgo para beneficios a largo plazo
        - **Propenso:** Flexible, dispuesto a asumir más riesgo para altos retornos con mayor tolerancia a fallas potenciales

*   **Tolerancia al Riesgo:** Más específica. Es la cantidad máxima de variación o desviación que una organización está dispuesta a aceptar alrededor del logro de un objetivo específico, o la cantidad máxima de riesgo que está dispuesta a aceptar para un riesgo individual particular. Sirve como umbral de alerta - si un riesgo excede la tolerancia definida, se debe tomar acción.

*   **Capacidad de Riesgo:** La cantidad total y tipo de riesgo que una organización es capaz de soportar sin poner en peligro su existencia o capacidad de continuar operando. Es el límite máximo absoluto. La tolerancia al riesgo para un evento específico siempre debe estar por debajo de la capacidad general de riesgo de la organización.

### Proceso de Gestión de Riesgos
1.  **Identificación de Riesgos:** ¿Qué puede salir mal? Se identifican riesgos en diversas categorías (estratégicos, financieros, operativos, de cumplimiento, de TI, etc.).

2.  **Evaluación de Riesgos:** Se analiza la **probabilidad** y el **impacto** de cada riesgo usando el método clásico del **mapa de riesgo inherente**. Es una matriz que grafica la probabilidad de que ocurra el riesgo (baja, media, alta) contra el impacto o severidad de sus consecuencias (baja, media, alta). Los riesgos se ubican en esta matriz para visualizarlos antes de aplicar cualquier control o respuesta. Los riesgos en la esquina superior derecha (alta probabilidad y alto impacto) son los más críticos.

3.  **Respuesta al Riesgo:** Se definen estrategias para llevar el **riesgo inherente** (riesgo bruto antes de controles) a un nivel de **riesgo residual** (riesgo después de controles) aceptable, idealmente dentro del apetito de riesgo. Las cuatro estrategias principales son:
    *   **Evitar:** Tomar acciones para que el riesgo simplemente no exista (ej. decidir no emprender una actividad que genera el riesgo, eliminar las causas)
    *   **Reducir/Mitigar:** Implementar controles y acciones para disminuir la probabilidad de que ocurra el riesgo o para reducir el impacto si ocurre (reducir a valores aceptables). Esta es la estrategia donde se aplican la mayoría de los controles internos.
    *   **Transferir/Compartir:** Trasladar parte del riesgo a un tercero (el ejemplo más común es contratar un seguro)
    *   **Aceptar:** Decidir no tomar ninguna acción para modificar el riesgo. Típicamente se hace para riesgos con baja probabilidad e impacto marginal, donde el costo de mitigación supera el beneficio esperado.

4.  **Monitoreo:** Revisar continuamente los riesgos y la efectividad de las respuestas. La gestión efectiva de riesgos es un ciclo continuo de identificar, evaluar, responder y monitorear riesgos, asegurando que el riesgo residual se mantenga dentro de la tolerancia y capacidad de la organización.

### Relación con otros temas
*   **COSO:** La evaluación de riesgos es uno de los cinco componentes del marco COSO.
*   **Controles (ITGC y de Proceso):** Son la principal herramienta para **mitigar** los riesgos identificados.
*   **BCP:** Es la respuesta específica para los riesgos de interrupción del negocio.
*   **Auditoría:** Evalúa si la organización gestiona adecuadamente sus riesgos y si los controles son efectivos.

---

## 12. COSO (Marco Integrado de Control Interno)

COSO es el marco de referencia globalmente aceptado para diseñar, implementar y evaluar la efectividad de un sistema de control interno.

### Definición y Objetivos
*   **Definición:** Un proceso, llevado a cabo por toda la organización, diseñado para proporcionar una **seguridad razonable** (no absoluta) sobre el logro de los objetivos.
*   **Tres Categorías de Objetivos:**
    1.  **Operacionales:** Eficacia y eficiencia de las operaciones.
    2.  **De Reporte (Reporting):** Fiabilidad de la información (financiera y no financiera).
    3.  **De Cumplimiento:** Adhesión a las leyes y regulaciones aplicables.

### Los 5 Componentes del Marco COSO (y sus 17 Principios)

#### 1. Ambiente de Control (Principios 1-5)
Es la base de todo el sistema. Define el "tono en la cima" de la organización e influye la conciencia de control de su gente. Incluye integridad, valores éticos, filosofía de la gerencia, estructura organizacional, asignación de autoridad y responsabilidad, y políticas y prácticas de recursos humanos.

**Principios:**
*   La organización demuestra compromiso con integridad y valores éticos
*   El Consejo de Administración demuestra independencia de la gerencia y ejerce supervisión del desarrollo y desempeño del control interno
*   La gerencia establece, con supervisión del consejo, estructuras, líneas de reporte y autoridades y responsabilidades apropiadas en la búsqueda de objetivos
*   La organización demuestra compromiso para atraer, desarrollar y retener individuos competentes en alineación con objetivos
*   La organización responsabiliza a individuos por sus responsabilidades de control interno en la búsqueda de objetivos

> **Crucial:** Un ambiente de control débil o falta de ética en la cima socava todos los demás controles.

#### 2. Evaluación de Riesgos (Principios 6-9)
La identificación y análisis de riesgos relevantes para lograr los objetivos predefinidos. Sirve como base para determinar cómo deben gestionarse los riesgos. Integra directamente el proceso de gestión de riesgos, incluyendo consideración de fraude y cambios relevantes.

#### 3. Actividades de Control (Principios 10-12)
Las políticas y procedimientos que la gerencia establece para asegurar que se tomen las acciones necesarias para mitigar riesgos identificados y asegurar que se cumplan los objetivos. Abarcan una amplia gama de acciones como aprobaciones, autorizaciones, verificaciones, reconciliaciones, salvaguarda de activos y segregación de funciones.

> **Conexión clave:** Aquí es donde encajan perfectamente los ITGC y los Controles de Proceso/SOD - son la materialización de estas actividades de control.

#### 4. Información y Comunicación (Principios 13-15)
Se refiere a la necesidad de identificar, capturar y comunicar información relevante de manera oportuna para permitir al personal llevar a cabo sus responsabilidades de control.

#### 5. Actividades de Monitoreo (Principios 16-17)
Evaluaciones (continuas o puntuales) para asegurar que los componentes del control interno están presentes y funcionando. La auditoría interna es un ejemplo clave de monitoreo.

### Limitaciones Inherentes del Control Interno
*   No puede dar seguridad absoluta.
*   Está sujeto a errores humanos.
*   Puede ser eludido por la **colusión** (acuerdo entre dos o más personas).
*   La dirección puede anular los controles (**management override**).

### Relación con otros temas
*   **Es el Marco Integrador:** COSO engloba todos los demás conceptos. La **Evaluación de Riesgos** se basa en ERM. Las **Actividades de Control** incluyen los ITGC y controles de proceso/SoD. El **Gobierno de TI** es parte del Ambiente de Control. La **Auditoría** utiliza COSO como modelo para evaluar el control interno.

---

## 13. Plan de Continuidad del Negocio (BCP)

El BCP es el conjunto de planes y estrategias que permiten a una organización seguir operando sus funciones críticas durante y después de un evento disruptivo.

### Conceptos Clave
*   **BCP (Business Continuity Plan):** El plan general y proactivo que cubre la continuidad de todo el negocio (procesos, personas, tecnología).
*   **BIA (Business Impact Analysis):** El análisis previo que identifica los procesos de negocio más críticos y el impacto (financiero y operacional) de su interrupción en el tiempo.
*   **DRP (Disaster Recovery Plan):** El plan reactivo y enfocado en TI. Es parte del BCP y detalla cómo recuperar la infraestructura tecnológica y los datos tras un desastre.

### Métricas Fundamentales (Definidas en el BIA)
*   **RTO (Recovery Time Objective):** Tiempo máximo tolerable para restaurar un proceso o sistema después de una interrupción. Define **cuán rápido** debe recuperarse.
*   **RPO (Recovery Point Objective):** Período máximo tolerable de pérdida de datos. Define **a qué punto en el tiempo** se debe recuperar la información (ej. al último backup).

### Proceso
1.  **BIA:** Identificar procesos críticos y definir sus RTO y RPO.
2.  **Estrategia de Continuidad:** Definir cómo se mantendrán esos procesos (ej. sitio alternativo, trabajo remoto).
3.  **Desarrollo del Plan (BCP/DRP):** Documentar los procedimientos, roles y recursos necesarios.
4.  **Pruebas y Mantenimiento:** Probar el plan regularmente (ej. simulacros de recuperación) para asegurar que funcione y mantenerlo actualizado.

### Relación con otros temas
*   **Gestión de Riesgos (ERM):** Es la respuesta directa a los riesgos de interrupción de operaciones de alto impacto.
*   **ITGC (Operaciones):** El DRP depende críticamente de los controles de operaciones, como los backups y los procedimientos de restauración.
*   **Amenazas y Ataques:** Un ciberataque exitoso es una de las principales amenazas que pueden activar un BCP/DRP.

---

## 15. Controles en Procesos de Negocio y Segregación de Funciones (SoD)

Estos controles, también llamados "controles de aplicación", operan a nivel de transacción dentro de un proceso de negocio específico. Su objetivo es asegurar la integridad de los registros.

### Ciclos de Negocio y Controles Típicos

#### Ciclo de Compras (Purchase-to-Pay)
*   **Subprocesos:** Pedido de compra, recepción de mercadería, procesamiento de facturas, pago.
*   **Riesgos:** Pagar por bienes no recibidos, pagar a proveedores ficticios, pagos duplicados.
*   **Controles Clave:**
    *   **3-Way Match:** Comparación de la Orden de Compra, la Recepción y la Factura antes de pagar.
    *   Aprobación de órdenes de compra.
    *   Reconciliaciones bancarias.

#### Ciclo de Ventas (Order-to-Cash)
*   **Subprocesos:** Toma de pedido, envío, facturación, cobranza.
*   **Riesgos:** Facturar incorrectamente, no facturar envíos, problemas de crédito de clientes.
*   **Controles Clave:**
    *   Validación del crédito del cliente antes de aceptar el pedido.
    *   Reconciliación entre envíos y facturas.
    *   Autorización de notas de crédito.

### Segregación de Funciones (SoD - Segregation of Duties)
*   **Principio Fundamental:** Ninguna persona debe tener la capacidad de ejecutar y ocultar un error o fraude por sí sola. Se deben separar las tareas incompatibles.
*   **Beneficios:** Reduce el riesgo de error y fraude, y actúa como un elemento disuasorio.
*   **Ejemplos de Funciones Incompatibles:**
    *   Crear un proveedor y procesar pagos a ese proveedor.
    *   Registrar una venta y autorizar la nota de crédito para esa venta.
    *   Tener la custodia de un activo y acceso a los registros contables de ese activo.
*   **Controles Compensatorios:** Controles alternativos (generalmente una revisión por parte de un supervisor) que se implementan cuando la SoD completa no es factible, para mitigar el riesgo.

### Relación con otros temas
*   **COSO:** Son el ejemplo perfecto de **Actividades de Control** a nivel transaccional.
*   **ITGC:** Los controles de aplicación dependen de un entorno tecnológico fiable, garantizado por los ITGC.
*   **Auditoría:** La evaluación de los controles de proceso y la SoD es un foco central de la auditoría de procesos y financiera.

---

## 16. Normas de Auditoría

Son el marco regulatorio que asegura la calidad, independencia y consistencia del trabajo de auditoría.

### Tipos de Normas
1.  **Normas de Auditoría Externa (Locales e Internacionales):**
    *   **RT 53 (Argentina - FACPCE):** Regula la auditoría de estados financieros. Define etapas (Planificación, Ejecución, Conclusión) y principios clave.
    *   **ISA (International Standards on Auditing):** Emitidas por el IAASB. Son las normas internacionales que buscan la convergencia global. La **ISA 315** es clave, ya que exige al auditor comprender la entidad y sus riesgos, incluyendo los de TI.
2.  **Normas de Auditoría Interna:**
    *   **Normas Globales de Auditoría Interna del IIA (2024):** Emitidas por el Institute of Internal Auditors (IIA). Establecen los principios para la práctica profesional de la auditoría interna. Se estructuran en 5 dominios: Propósito, Ética y Profesionalismo, Gobernanza, Gestión y Desempeño.

### Principios Fundamentales de Auditoría
*   **Independencia:** El auditor debe ser independiente en mente y apariencia.
*   **Escepticismo Profesional:** Una actitud mental crítica y de cuestionamiento. No asumir que todo está bien sin evidencia.
*   **Evidencia de Auditoría Suficiente y Apropiada:** Base para las conclusiones del auditor.
*   **Documentación:** El trabajo del auditor debe estar debidamente documentado en papeles de trabajo.

### Tipos de Opinión (Auditoría Externa)
*   **No Modificada (Limpia):** Los estados financieros presentan razonablemente la realidad.
*   **Modificada:**
    *   **Con Salvedades:** Presentan razonablemente, *excepto por* un tema específico.
    *   **Adversa:** No presentan razonablemente la realidad.
    *   **Abstención de Opinión:** El auditor no pudo obtener evidencia para opinar.

### Relación con otros temas
*   **Son el "Reglamento":** Definen cómo el auditor debe planificar y ejecutar su trabajo (el siguiente tema) para evaluar todos los demás conceptos (COSO, ITGC, ERM, etc.).

---

## 17. Planificación, Ejecución y Pruebas de Auditoría

Describe el proceso práctico que sigue un auditor para realizar su trabajo.

### Etapas del Proceso
1.  **Planificación:**
    *   Entender la entidad, su entorno, sistemas y control interno (COSO).
    *   Evaluar los riesgos (ERM) para identificar áreas de foco.
    *   Definir el alcance, la estrategia y los procedimientos de auditoría.
2.  **Ejecución:**
    *   Realizar las pruebas planificadas para obtener evidencia.
3.  **Conclusión e Informe:**
    *   Evaluar la evidencia obtenida.
    *   Formar una opinión o conclusión.
    *   Emitir un informe con los hallazgos.

### Tipos de Pruebas de Auditoría
*   **Pruebas de Controles:** Se realizan para evaluar la **eficacia operativa** de un control. El objetivo es ver si el control funcionó como se diseñó a lo largo del período.
*   **Pruebas Sustantivas:** Se realizan para detectar errores materiales en los saldos de los estados financieros. Pueden ser:
    *   **Procedimientos Analíticos:** Análisis de tendencias y ratios.
    *   **Pruebas de Detalle:** Examen de transacciones y saldos individuales.

### El "Ciclo de Confianza" y el Walkthrough
*   Para confiar en los controles de una entidad (y así reducir las pruebas sustantivas), el auditor sigue un ciclo:
    1.  **Entender** el control.
    2.  **Evaluar su diseño** (¿es adecuado para mitigar el riesgo?).
    3.  **Probar su eficacia operativa** (¿funcionó en la práctica?).
*   El **Walkthrough (Prueba de Recorrido)** es la técnica clave para el paso 1 y 2. Consiste en seguir una única transacción de principio a fin a través del proceso para confirmar el entendimiento y evaluar el diseño de los controles.

### Documentación del Entendimiento
*   Se utilizan herramientas como **narrativas** (descripciones escritas), **diagramas de flujo** (representaciones visuales) y **matrices de riesgo y control**.

### Relación con otros temas
*   **Es la Práctica:** Es la aplicación de las **Normas de Auditoría** para evaluar el **Control Interno (COSO)**, los **ITGC** y los **Controles de Proceso**. La **Gestión de Riesgos (ERM)** guía la planificación.

---

## 18. Seguridad en el SDLC y DevSecOps

Este tema se centra en construir seguridad en el software desde su concepción, en lugar de añadirla al final.

### Conceptos Clave
*   **SDLC (Software Development Lifecycle):** El proceso estructurado para diseñar, desarrollar, probar y mantener software.

*   **Metodologías de Desarrollo:**
    *   **Tradicional/Waterfall:** Sigue fases secuenciales (iniciación del proyecto, análisis de requerimientos y diseño, construcción/codificación, pruebas y QA, conversión de datos, implementación final). En este modelo lineal, las actividades rigurosas de seguridad y pruebas a menudo ocurren tarde en el ciclo, haciendo costoso y difícil corregir problemas encontrados posteriormente.
    *   **Ágil/Scrum:** Marco para gestionar proyectos complejos usando un enfoque iterativo e incremental. Elementos clave:
        - **Roles:** Scrum Master (facilita el equipo), Product Owner (representa el negocio y define el "qué"), Equipo de Desarrollo (hace el "cómo")
        - **Artefactos:** Product Backlog (lista priorizada de características), Sprint Backlog (tareas para un ciclo corto o sprint), Definición de Terminado (define cuándo algo está completo)
        - **Eventos:** Sprint Planning (planificar el sprint), Daily Scrum (sincronización diaria), Sprint Review (presentar trabajo terminado), Sprint Retrospective (mejorar el proceso)

*   **Shift Left:** La práctica de mover las actividades de seguridad lo más temprano posible en el ciclo de desarrollo. Es más barato y efectivo corregir fallos en las etapas iniciales.

*   **DevSecOps:** Una cultura y un conjunto de prácticas que integran la seguridad (**Sec**) de forma automatizada en el ciclo de vida de DevOps (Desarrollo - **Dev** y Operaciones - **Ops**).
    *   Mientras que en un modelo ágil puro, la seguridad podría ocurrir después del despliegue en un ciclo continuo, DevSecOps integra la seguridad en cada fase del ciclo de vida desde la planificación inicial hasta la operación continua
    *   La seguridad no es un paso final, es una responsabilidad compartida e integrada de todos, no de un equipo aislado

### Herramientas de Seguridad Automatizada en DevSecOps
*   **SAST (Static Application Security Testing):** Analiza el código fuente en busca de vulnerabilidades sin ejecutarlo.
*   **DAST (Dynamic Application Security Testing):** Prueba la aplicación mientras se está ejecutando, simulando ataques externos.
*   **SCA (Software Composition Analysis):** Analiza las librerías de terceros y componentes de código abierto utilizados en el proyecto para identificar vulnerabilidades conocidas.
*   **Fuzzing:** Envía datos inválidos, inesperados o aleatorios a una aplicación para provocar fallos.

### Controles Específicos para Cambios de Programas
Para mitigar riesgos en el proceso de cambios, los controles incluyen:

*   **Controles Preventivos:**
    *   Mantener separación adecuada de ambientes (DEV/QA/PROD)
    *   Asegurar segregación apropiada de funciones (ej. prevenir que personal de desarrollo tenga permisos para mover cambios a producción)
    *   Verificar que cada cambio fue formalmente solicitado y cumple especificaciones
    *   Verificar que se generó un documento de diseño funcional aprobado
    *   Verificar que el cambio fue probado exhaustivamente en el ambiente de pruebas/QA, incluyendo pruebas de aceptación del usuario
    *   Verificar que el cambio tiene la autorización formal requerida antes de moverse al ambiente de producción
    *   Si el cambio involucra migración de datos, verificar que fue probado y documentado

*   **Proceso Formal:** Las organizaciones deben tener un proceso formal, documentado y definido para desarrollo y cambios de programas. Esto asegura que se sigan los pasos requeridos y que los controles estén presentes en cada etapa.

### Modelo de Amenazas STRIDE
Es un modelo para clasificar las amenazas a la seguridad del software:
*   **S**poofing (Suplantación) -> Mitigado por **Autenticación**.
*   **T**ampering (Manipulación) -> Mitigado por **Integridad**.
*   **R**epudiation (Repudio) -> Mitigado por **No Repudio**.
*   **I**nformation Disclosure (Divulgación de Información) -> Mitigado por **Confidencialidad**.
*   **D**enial of Service (Denegación de Servicio) -> Mitigado por **Disponibilidad**.
*   **E**levation of Privilege (Elevación de Privilegios) -> Mitigado por **Autorización**.

### Relación con otros temas
*   **ITGC (Cambios):** Es la implementación de controles de seguridad dentro de este dominio.
*   **Amenazas y Ataques:** Busca construir software que sea inherentemente resistente a estas amenazas.
*   **Gestión de Riesgos (ERM):** Es una respuesta proactiva para mitigar los riesgos asociados a vulnerabilidades en el software.

---

## 19. Tipos de Amenazas y Ataques

Este tema detalla los peligros y las técnicas que los actores maliciosos utilizan para comprometer los sistemas.

### Conceptos Clave
*   **Amenaza:** Cualquier circunstancia o evento con el potencial de causar daño.
*   **Superficie de Ataque:** El conjunto total de puntos de entrada que un atacante podría usar para comprometer un sistema.
*   **IOC (Indicator of Compromise):** Evidencia forense que indica que un ataque ha ocurrido (ej. un hash de malware conocido, una IP maliciosa).
*   **IOA (Indicator of Attack):** Evidencia que indica que un ataque está en progreso (ej. un movimiento lateral en la red).

### Tipos de Malware
*   **Ransomware:** Cifra los datos de la víctima y exige un rescate para restaurarlos.
*   **Spyware:** Espía las actividades del usuario sin su conocimiento.
*   **Trojan (Troyano):** Se disfraza de software legítimo pero contiene una carga maliciosa.
*   **Worm (Gusano):** Se replica y propaga a través de redes sin intervención del usuario.

### Ataques Más Frecuentes
*   **Ransomware:** Uno de los ataques más dañinos y lucrativos.
*   **Phishing:** La puerta de entrada más común, basada en ingeniería social.
*   **Fuga de Datos (Data Breach):** El robo o exposición de información sensible.
*   **Ingeniería Social:** La manipulación psicológica de las personas. Es la base de muchos otros ataques.

### Marco de Referencia
*   **MITRE ATT&CK:** Una base de conocimiento global de tácticas y técnicas de adversarios basadas en observaciones del mundo real. Es un "mapa" de cómo operan los atacantes.

### Relación con otros temas
*   **Son el "Enemigo":** Son los eventos adversos que **ERM** busca gestionar y que los **controles (ITGC, Awareness, SDLC)** buscan prevenir.
*   **BCP/DRP:** Se activan cuando una de estas amenazas causa una interrupción mayor.
*   **Detección y Respuesta:** Se encarga de identificar y manejar los incidentes cuando una amenaza se materializa.

---

## 20. Detección y Respuesta a Incidentes de Seguridad

*Nota: Este tema no fue desarrollado en detalle en la información fuente, por lo que se presenta un resumen conceptual.*

A pesar de los controles preventivos, es inevitable que ocurran incidentes. La capacidad de una organización para detectarlos rápidamente y responder de manera efectiva es crucial.

### Proceso
1.  **Detección:** Monitorear sistemas y redes en busca de actividad sospechosa (basado en IOCs y IOAs). Esto es a menudo realizado por un **SOC (Security Operations Center)**.
2.  **Respuesta:** Un equipo especializado (**CSIRT - Computer Security Incident Response Team**) toma acción. Las fases típicas son:
    *   **Contención:** Aislar el sistema afectado para que el incidente no se propague.
    *   **Erradicación:** Eliminar la causa raíz del incidente (ej. el malware).
    *   **Recuperación:** Restaurar los sistemas a un estado seguro y operativo.
    *   **Lecciones Aprendidas:** Analizar el incidente para mejorar las defensas futuras.

### Relación con otros temas
*   **Es el Plan Reactivo:** Actúa cuando los controles preventivos fallan.
*   **Amenazas y Ataques:** Es la respuesta directa a la materialización de una amenaza.
*   **BCP/DRP:** La fase de recuperación de un incidente mayor a menudo se alinea con los procedimientos del DRP.

---

## 21. Pruebas de Penetración y Auditoría de Código

*Nota: Este tema no fue desarrollado en detalle en la información fuente, por lo que se presenta un resumen conceptual.*

Son técnicas proactivas de evaluación de seguridad para encontrar debilidades antes de que lo hagan los atacantes.

### Técnicas
*   **Pruebas de Penetración (Pentesting):**
    *   Un ataque simulado y autorizado contra un sistema para identificar vulnerabilidades explotables.
    *   Utiliza diferentes enfoques según el nivel de conocimiento del "atacante":
        *   **Black Box:** Sin conocimiento previo del sistema.
        *   **White Box:** Con conocimiento total (código fuente, arquitectura).
        *   **Gray Box:** Con conocimiento parcial (ej. credenciales de usuario estándar).
*   **Auditoría de Código (Code Review):**
    *   Una revisión manual o automatizada del código fuente de una aplicación para identificar fallos de seguridad, lógica y programación insegura. Está directamente relacionada con las herramientas **SAST**.

### Relación con otros temas
*   **SDLC y DevSecOps:** Son técnicas de verificación y validación que se integran en el ciclo de desarrollo para asegurar la calidad del software.
*   **Auditoría:** Pueden ser realizadas por auditores de seguridad o el auditor puede revisar que la organización las realice periódicamente y gestione los hallazgos.
*   **Gestión de Riesgos (ERM):** Ayudan a identificar y validar la existencia de riesgos técnicos.

---


## 9. ITGC y Gobierno TI

### Conceptos Principales

* Seguridad Informática y
* Principios de Auditoría
* Gobierno de IT y Controles Generales de TI (ITGC)
* Profesor Pablo Gil
* Gobierno de IT (Tecnologías de Información)
* Empresas y sus partes interesadas
* Las empresas existen para crear valor
* para sus partes interesadas
* (Stakeholders).
* En consecuencia, cualquier organización

### Puntos Clave

* tendrá la creación de valor como un
* objetivo de gobernabilidad. La creación
* de valor significa obtener beneficios a un
* costo de recursos óptimo mientras se
* optimizan los riesgos.
* UBA FCE –Seguridad Informática y Principios de Auditoría Slide 2
* Gobierno de IT (Tecnologías de Información)
* El Gobierno de IT hace referencia al conjunto de estructuras y procesos que una
* organización gestiona, de manera eficiente y segura, para administrar y dirigir sus
* sistemas, que deben estar alineados y considerar además el cumplimiento de los

### Información Adicional

objetivos estrategicos de la Alta Dirección (Directorio y Accionistas) y Grupos de
interes Internos (el Negocio) o Externos (clientes, proveedores, otros) afectados por la
actividad de la organización.
Elementos clave del Gobierno de IT:
✓ Alineación estratégica
✓ Roles y responsabilidad (para la gestión de las TI)
✓ Gestión de riesgos
✓ Gestión de recursos
✓ Cumplimiento de leyes y regulaciones
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 3
Gobierno de IT (Tecnologías de Información)
Gobierno de IT vs Gestión de IT
▪ El gobierno garantiza que las necesidades, las condiciones y las opciones de las
partes interesadas se evalúen para determinar las metas empresariales equilibradas
y acordadas que se deben alcanzar; establece la dirección mediante la priorización y
la toma de decisiones; y monitorea el desempeño y el cumplimiento de acuerdo con
la dirección y los objetivos acordados.
▪ La gestión (gerencia) planea, construye, ejecuta y monitorea las actividades en
consonancia con la dirección establecida por el órgano de gobierno para alcanzar
las metas empresariales.
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 4
Gobierno de IT (Tecnologías de Información)
Principales funciones del Gobierno de IT
El Gobierno corporativo y el Gobierno de IT son ambos componentes del
Gobierno global de una organización.
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 5
Gobierno de IT (Tecnologías de Información)
Principales Marcos de trabajo y buenas prácticas
➢ ISO/IEC 38500:2008: Corporate governance of information technology,
(basado en AS8015-2005), define un marco de trabajo para el gobierno de TI
➢ COBIT (Control Objectives for Information and related Technology): Es
un modelo de referencia que describe 34 procesos relacionados con TI y que son
comunes a todas las organizaciones.
➢ AS8015-2005: Estándar australiano para el gobierno corporativo de la tecnología
de la información y las comunicación
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 6
Actividades de Gobierno del área de Sistemas
En resumen:
• Estructura y dependencia del área de Sistemas – segregación de
funciones
• Plan estratégico de sistemas – definición y seguimiento
• Gestión de riesgos de TI – identificación de todos los riesgos
• Definición de Políticas y procedimientos - definición de tareas y
controles
• Gestión de relaciones con terceros – proveedores de servicios
• Cumplimiento – certificaciones leyes y regulaciones aplicables
• Métricas de evaluación TI – efectividad del gobierno
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 7
Controles Generales de TI (ITGC)
EEFF
Aserciones
financieras
Operación del negocio Reporte
Principales clases de transacciones, procesos y subprocesos de negocio financiero
•Exactitud
) •Integridad
s Riesgos asociados al procesamiento de transacciones
t •Corte
r •Existencia y
i Consideraciones del proceso
n ocurrencia
d •Derechos y
Controles a nivel de entidad (directos)
obligaciones
n •Presentación y
e Controles a nivel de transacción
divulgación
Procedimientos y Controles manuales
i n controles automáticos dependientes de IT Controles manuales •Valuación
o Controles generales de TI (CGTI)
Riesgos asociados al uso de sistemas Segregación
de funciones
Ambiente de TI
Aplicaciones e
Datos
infraestructura
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 8
Controles Generales de TI (ITGC)
Políticas, procedimientos y actividades de control que
se relacionan con varios sistemas/aplicaciones y
t soportan el funcionamiento efectivo de los controles a
i nivel de transacción.
e Controles a nivel de transacción
Procedimientos y Controles manuales
Controles manuales
i n controles automáticos dependientes de IT
o Controles generales de TI (CGTI)
Riesgos asociados al uso de sistemas
Ambiente de TI
Aplicaciones e
Datos
infraestructura
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 9
Controles Generales de TI (ITGC)
Los ITGC son relevantes ya que constituyen el ambiente de generación
y procesamiento de:
• Cálculos realizados por las aplicaciones
• Reportes generados por las aplicaciones
• Controles automáticos
• Seguridad lógica (incluyendo segregación de funciones)
• Interfases entre aplicaciones
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 10
Controles Generales de TI (CGTI)
Riesgos que surgen del ambiente de TI
Acceso al software y a los datos
• Accesos no autorizados a las aplicaciones por parte de usuarios de negocio
• Accesos no autorizados a las aplicaciones y a los datos por parte de usuarios de TI
• Cambios no autorizados a los datos
Cambios a los programas
• Cambios no solicitados por el negocio
• Cambios que introducen errores en las aplicaciones
• Cambios directos y no autorizados a los programas
• Cambios directos y no autorizados a la configuración del sistema
• Actualizaciones inadecuadas de sistemas operativos y bases de datos
Desarrollo de sistemas
• Los sistemas implementados procesan datos de manera incorrecta debido a problemas de
codificación o configuración
• Errores al migrar registros de transacciones y/o datos maestros
Operaciones computarizadas
• Fallas en los sistemas que causan pérdida de datos o transacciones o incapacidad para
acceder a ellos según se requiera
• Intervención manual inapropiada o fallas en el procesamiento de trabajos programados
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 11
Controles Generales de TI (CGTI)
Acceso al software y a los datos
Administración y monitoreo de la seguridad
u Datos Aplicaciones
r Sistema operativo
e Red interna
Red perimetral
Seguridad física
Administración de accesos – usuarios de negocio
Administración de accesos – usuarios técnicos
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 12

### Relación con otros temas
*   **Conexión con el marco general:** Este tema se integra con los conceptos de seguridad y auditoría del curso.

---


## 10. Cyber Threat Intelligence y SOC Radar

### Conceptos Principales

* socradar.io
* Proactive Security with
* Early-Warning
* System
* Fernando Imperiale
* Technical Account Manager - LATAM
* Agenda
* 01 Who is SOCRadar?
* 02 Problems and Recommended Solution
* 03 What is XTI?

### Puntos Clave

* 04 How SOCRadar Works
* 05 Use Cases
* Integrations
* About SOCRadar
* 2019 20.000+ 300+
* Founded Users Partners
* in 100 countries in 110+ countries
* top 2
* Ranked among the
* Referred in the reports of:

### Información Adicional

Market Guide, Hype Cycle and
several others
socradar.io
CHALLENGES SOLUTIONS RESULTS
Ransomware Secure
Systems
Automation
Business Email Compromise
Early Warning System
Phishing
Happy Security
Teams
Cloud Security
Contextualized Intelligence
Third Party
(Supply Chain Attacks)
Actionable Advice
Account Takeover
socradar.io
Solutions
Extended Threat
Intelligence
(XTI)
SOCRadar Extended Threat Intelligence, a natively single
platform from its inception that proactively identifies and
analyzes cyber threats with contextual and actionable
intelligence.
Maximize the efficiency of your SOC team with
false-positive free, actionable, and
contextualized threat intelligence.
socradar.io
How SOCRadar Works?
Surface / Dark Web Digital Footprint
Threat Actors' Forums
Threat Feed & IOC Management
Hackers’ Communication Channels
Digital Asset Monitoring
Social Media Platforms Human Analyst
Paste Sites
Machine Learning
Credential Stuffing Protection
IoCs and Vulnerabilities
Leaked Credit Card Detection
Code Repositories
Cloud Buckets +Tens of Modules
socradar.io
See behind the
shadows:
Wherever threat actors
are, so are we.
SOCRadar XTI continuously
monitors Telegram
Channels, Discord Servers,
Hacker Forums along with
numerous
TOR sites and paste sites ;
socradar.io
Key Use Cases
Brand Protection Dark Web Monitoring Data Leak Detection
• Phishing & BEC • Stealer Logs / Session Cookies • Combolists
• VIP Protection • Track Ransomware • Code Leak Monitoring
• Rogue Mobile Apps • Stolen data detection • Sale of Employee Credentials
• Social Media Impersonation • Black Markets, Telegram • Sensitive Corporate Documents
• Takedowns • Threat Actor Profiling
Cyber Threat Intelligence Attack Surface Management Incident Management
• APT & Threat Actor Tracking • Vulnerability Prioritization • Incident & Alert Enrichment
• Vulnerability Intelligence • Scanning Digital Footprint • Breached Customer Data Monitoring
• Supply Chain Vendor Tracking • Shadow IT Detection • Malware Analysis & Threat Hunting
• Geographic Threats • Misconfigured Network Detection • Cyber forensics
• Breach Monitoring
Use Cases
Dark Web Ransomware Data Leak VIP Threat Actor Phishing Exposed
Monitoring Prevention Detection Protection APT Tracking Intelligence Vulnerability
Detection
Critical Open Code Leak Threat Hunting Shadow Third Party Social Media Fraud
Port Alert Detection and Investigation IT Monitoring Monitoring Protection
socradar.io
Integrations
SIEM SOAR Firewall Intelligence Sharing Platform DDoS Protection Products
CISCO Products
Vulnerability Management
Products
DNS Security
Work –Service Management &
Ticketing
Team Management APPS
Email Security
socradar.io
THANK YOU
Fernando Imperiale
Technical Account Manager
socradar.io

### Relación con otros temas
*   **Conexión con el marco general:** Este tema se integra con los conceptos de seguridad y auditoría del curso.

---


## 11. Gestión de riesgos ERM

### Conceptos Principales

* Seguridad Informática y Principios de Auditoría
* Gestión de riesgos - ERM
* Profesor Pablo Gil
* Agenda
* • Gestión de Riesgos
* • Marcos de Gestión de Riesgos
* • Conceptos relevantes Gestión de Riesgos
* • Ejemplo software ERM
* UBA FCE –Seguridad Informática y Principios de Auditoría
* ¿QUÉ PUEDE SALIR MAL?

### Puntos Clave

* UBA FCE –Seguridad Informática y Principios de Auditoría
* LAS TRES LÍNEAS
* Fuente: “The three lines model” IIA - Institute of Internal Auditors
* UBA FCE –Auditoría y Seguridad de los Sistemas de Información
* GESTIÓN DE RIESGOS
* RIESGO
* Posibilidad de existencia de un evento adverso, expresado en términos de la combinación de
* • su probabilidad de ocurrencia
* • la mayor severidad de sus consecuencias (razonablemente esperables)
* Todos los objetivos y desafíos organizacionales tienen implícitos ciertos riesgos sean ellos directos o indirectos

### Información Adicional

UBA FCE –Auditoría y Seguridad de los Sistemas de Información
GESTIÓN DE RIESGOS
¿PORQUÉ GESTIONAR RIESGOS?
• Para prevenir su ocurrencia
• Para reaccionar rápidamente ante situaciones de aquellas circunstancias –
internas o externas – que pueden influir en el logro de los objetivos de la
organización
✓ continuidad operativa
✓ pérdidas económicas (incluyendo impactos sancionatorios)
✓ daños a bienes
✓ lesiones a personas
✓ impactos ambientales
✓ impactos reputacionales
UBA FCE –Auditoría y Seguridad de los Sistemas de Información
MARCOS DE GESTIÓN DE RIESGOS – ISO
UBA FCE –Auditoría y Seguridad de los Sistemas de Información
MARCOS DE GESTIÓN DE RIESGOS – ISO
UBA FCE –Auditoría y Seguridad de los Sistemas de Información
MARCOS DE GESTIÓN DE RIESGOS – ISO
UBA FCE –Seguridad Informática y Principios de Auditoría
MARCOS DE GESTIÓN DE RIESGOS – COSO
• Debido a la preocupación por la administración de riesgos, The Committee of Sponsoring Organisations of the
Treadway Commission determinó la necesidad de que exista un marco reconocido de administración integral de
riesgos.
• El proyecto se inició en enero de 2001 con el objeto de desarrollar un marco global para evaluar y mejorar el
proceso de administración de riesgos, reconociendo que muchas organizaciones están comprometidas en este
aspecto.
UBA FCE –Auditoría y Seguridad de los Sistemas de Información
MARCOS DE GESTIÓN DE RIESGOS – COSO
• En septiembre de 2004, se publicó el informe denominado
Enterprise Risk Management – Integrated Framework, el cual
incluye el marco global para la administración integral de riesgos.
• Enterprise Risk Management - Integrated Framework incluyó el
control interno, por lo que en ningún caso reemplazaba a Internal
Control - Integrated Framework.
UBA FCE –Auditoría y Seguridad de los Sistemas de Información
MARCOS DE GESTIÓN DE RIESGOS – COSO
Marco de Gestión de Riesgo Empresarial-
Marco de Control Interno-Integrado
Integrado
• Incluía la categoría de objetivos estratégicos de la organización y la dimensión de “Determinación de
objetivos”.
• Expandía la evaluación del riesgo en tres componentes: “Identificación de eventos”, “Evaluación del
riesgo” y “Respuesta a los riesgos”
UBA FCE –Auditoría y Seguridad de los Sistemas de Información
MARCOS DE GESTIÓN DE RIESGOS – COSO
• En junio de 2017 se publica una nueva versión de COSO ERM,
Enterprise Risk Management Integrating with Strategy and
Performance, el cual intenta mejorar el enfoque de la gestión de riesgos
como una manera de crear, preservar, mantener y generar valor en la
organización.
• Conformado por 20 principios organizados en 5 componentes
interrelacionados. Los principios describen prácticas que pueden
aplicarse de diversas maneras para diferentes organizaciones sin
importar su tamaño, tipo o sector. La adhesión a estos principios puede
proporcionar una expectativa razonable de que la organización entiende
y se esfuerza por gestionar los riesgos asociados con su estrategia y sus
objetivos empresariales o de negocio.
UBA FCE –Auditoría y Seguridad de los Sistemas de Información
MARCOS DE GESTIÓN DE RIESGOS – COSO
COSO ERM
UBA FCE –Auditoría y Seguridad de los Sistemas de Información
CONCEPTOS RELEVANTES GESTIÓN DE RIESGOS
APETITO DE RIESGO
Cantidad de riesgo que una organización desea asumir en la consecución de sus objetivos, permitiendo, además de
optimizar el binomio riesgo-rentabilidad, mantener los riesgos dentro de los niveles deseados. Ayuda a alinear la
organización, las personas y los procesos, en el diseño de la infraestructura necesaria para responder eficazmente y
monitorear los riesgos.
TOLERANCIA AL RIESGO
Cantidad máxima de un riesgo que una organización está dispuesta a aceptar para lograr su objetivo. Se refiere a lo que
una organización se puede permitir al gestionar riesgos y que, en caso de aparecer, tiene que ser capaz de soportar.
También sirve como una alerta para evitar llegar a la capacidad de riesgo.
CAPACIDAD DE RIESGO
Cantidad y tipo de riesgo que una organización es capaz de soportar en la persecución de sus objetivos. En caso de que la
organización supere su capacidad de riesgo se encontraría en serias dificultades para continuar con su actividad.
UBA FCE –Auditoría y Seguridad de los Sistemas de Información
CONCEPTOS RELEVANTES GESTIÓN DE RIESGOS
UBA FCE –Auditoría y Seguridad de los Sistemas de Información
CONCEPTOS RELEVANTES GESTIÓN DE RIESGOS
APETITO DE RIESGO
Factores a considerar
• Modelo de negocio y estrategia.
• Perfil de riesgo de la organización y proceso actual de gestión de riesgos
• Cultura y concientización de la organización frente a los riesgos
• Principales riesgos inherentes
• Naturaleza y el alcance de los riesgos dispuestos a asumir en la búsqueda de objetivos estratégicos
• Equilibrio entre los riesgos y las oportunidades de desarrollo del negocio
UBA FCE –Auditoría y Seguridad de los Sistemas de Información
CONCEPTOS RELEVANTES GESTIÓN DE RIESGOS
Categorías apetito de riesgo (ejemplo)
INTOLERANTEEnfoqueestrictaeintencionalmente cautelosoyconservador,porloquelatoleranciaaresultadosinciertosen
relaciónconellogrodelamisión,visiónuobjetivosesbajaylimitada.
La organización no está dispuesta a aceptar ningún riesgo, ni ningún impacto negativo para perseguir objetivos estratégicos. Se
evitanaquellosriesgosqueno puedensertratadosotransferidoseficazmente.
AVERSO Enfoque moderado, conservador y medido respecto a la tolerancia hacia los resultados inciertos en relación con el
logrodelamisión,lasmetasolosobjetivos.Setoleraránciertosriesgosseleccionadosquedebenestarsólidamentejustificados.
Los riesgosse aceptarán solo si son esencialesy siexisteuna posibilidad limitada de queocurraun evento negativo, siempre que
losmismossepuedanmonitorear,accionar yresolverrápidamente.
TOLERANTE Existe una predisposición al riesgo para obtener beneficios a largo plazo que son previsibles y superan dichos
riesgos,pudiendo intercambiaresteobjetivocon ellogrodeotrosobjetivos.
Inclinación a asumir riesgos justificados para lograr retornos mensurables. Dispuesto a cambiar los objetivos estratégicos para
obtenerestosretornos.
PROPENSO Enfoque más flexible y por ende con mayor posibilidad de falla, existiendo más predisposición a resultados
inciertosenrelaciónconellogrodelamisión, metasuobjetivos.
Posibilidad de tomar de manera proactiva decisiones innovadoras o pioneras y adoptar formas de implementación estratégica,
asumiendoyaceptando losriesgosasociados,paralograraltosretornos.Dispuestoaaceptarimpactosnegativos.
UBA FCE –Auditoría y Seguridad de los Sistemas de Información
CONCEPTOS RELEVANTES GESTIÓN DE RIESGOS
Categorías de riesgo
Categoría Subtipos
Entorno • Políticos y Sociales
• Económicos
• Tecnología de la Información
• Comportamiento de Clientes
• Competidores
• Cambios Regulatorios Adversos
Estratégicos • Estrategias Comerciales
• Rentabilidad del Negocio
• Política de Costos
• Políticas de Precios
• Continuidad del Negocio
• Dependencia hacia proveedores
• Dependencia hacia Clientes
• Fusiones y Adquisiciones
• Desarrollo y Retención de talento
UBA FCE –Auditoría y Seguridad de los Sistemas de Información
CONCEPTOS RELEVANTES GESTIÓN DE RIESGOS
Categorías de riesgo
Categoría Subtipos
Financieros • Tipo de Cambio
• Tasa de Interés
• Riesgo País
• Liquidez
• Solvencia
Contables y fiscales • Impositivos
• Contabilidad e Información Financiera
• Activos
• Activos Intangibles
Crédito • Crédito de contraparte
Clientes • Marketing & Ventas
• Calidad de Productos y Servicios
• Atención al cliente
Procesos • Diseño inadecuado de procesos
UBA FCE –Auditoría y Seguridad de los Sistemas de Información
CONCEPTOS RELEVANTES GESTIÓN DE RIESGOS
Categorías de riesgo
Categoría Subtipos
Ingresos y costos • Proceso de Ingresos
• Desviación de costos
TI • Infraestructura Tecnológica
• Redes y Telecomunicaciones
• Desarrollo y Mantenimiento de Software
Seguridad de la información • Integridad de Datos
• Disponibilidad de Datos
• Confidencialidad de Datos
• Responsabilidad y trazabilidad de la información
Comunicaciones • Comunicaciones internas y externas
Compras y contratos • Selección y seguimiento de proveedores
• Formalización de contrato
• Gestión de obligaciones contractuales
• Subcontratación de servicios
UBA FCE –Auditoría y Seguridad de los Sistemas de Información
CONCEPTOS RELEVANTES GESTIÓN DE RIESGOS
Categorías de riesgo
Categoría Subtipos
Fraude interno
Fraude externo
Reputacional • Reputación y responsabilidad corporativa
Cumplimiento • Políticas y estándares internos
• Leyes y regulaciones
• Reclamos contra la empresa
• Corrupción y comportamiento no ético
Protección de datos personales • Principios y requisitos legales para el tratamiento de
datos personales
• Atención a los derechos de los titulares de los datos
PLD y FT • Lavado de dinero
• Financiamiento del terrorismo
UBA FCE –Auditoría y Seguridad de los Sistemas de Información
CONCEPTOS RELEVANTES GESTIÓN DE RIESGOS
Evaluación de riesgos - EJEMPLO
Mapa de riesgos inherentes
1 2 3 4 5
PROBABILIDAD
UBA FCE –Auditoría y Seguridad de los Sistemas de Información
CONCEPTOS RELEVANTES GESTIÓN DE RIESGOS
Estrategia de respuesta a riesgos
Estrategia Descripción
“Suprimir las causas”
Se implementan las acciones para hacer que las
Evitar / Eliminar
condiciones/factores que pueden generar el riesgo
desaparezcan, y con ellos, el riesgo.
“Disminuir a valores aceptables”
Reducir / Mitigar Implementar controles que lleven la clasificación del riesgo a
niveles más aceptables en cuanto a impacto o probabilidad
“Pasar el problema a alguien más”
Transferir / Compartir Lo más usual es contratar una póliza de seguros que indemnice a
la organización en caso de que se presente el problema.
“No hacer nada”
Generalmente las organizaciones deciden aceptar un riesgo
Aceptar
cuando este es de muy baja probabilidad de ocurrencia y/o el
impacto es marginal.
UBA FCE –Auditoría y Seguridad de los Sistemas de Información
CONCEPTOS RELEVANTES GESTIÓN DE RIESGOS
Riesgo inherente: riesgo existente sin considerar ninguna acción, respuesta o
control para alterar la probabilidad o el impacto de un suceso
Riesgo residual: riesgo que persiste el que persiste luego de considerar la
respuesta al riesgo. Para determinar el riesgo residual se deben realizar
adecuadas evaluaciones de respuestas a los riesgos.
UBA FCE –Auditoría y Seguridad de los Sistemas de Información
CONCEPTOS RELEVANTES GESTIÓN DE RIESGOS
Fraude
UBA FCE –Auditoría y Seguridad de los Sistemas de Información
CONCEPTOS RELEVANTES GESTIÓN DE RIESGOS
Fraude
https://legacy.acfe.com/report-to-the-nations/2024/
UBA FCE –Auditoría y Seguridad de los Sistemas de Información
EVALUANDO LAS ESCALAS DE MADUREZ EN LA
GESTIÓN DE RIESGOS
Fuente:
UBA FCE –Auditoría y Seguridad de los Sistemas de Información
EJEMPLOS SOFTWARE ERM
https://www.youtube.com/watch?v=UIg4pwY5k6A
UBA FCE –Auditoría y Seguridad de los Sistemas de Información

### Relación con otros temas
*   **Conexión con el marco general:** Este tema se integra con los conceptos de seguridad y auditoría del curso.

---


## 12. COSO

### Conceptos Principales

* Seguridad Informática y Principios de Auditoría
* COSO
* Profesor Pablo Gil
* Agenda
* • Definición de Sistema de Control Interno
* • COSO – Control interno
* • Componentes y principios
* UBA FCE –Seguridad Informática y Principios de Auditoría Slide2
* Sistema de Control Interno
* • Un sistema de control interno es un proceso efectuado por el consejo de administración,

### Puntos Clave

* el directorio, la gerencia y el resto del personal de una entidad, diseñado para proporcionar
* una seguridad razonable acerca del logro de objetivos en las siguientes categorías:
* • Eficacia y eficiencia de las operaciones
* • Confiabilidad de la información (financiera y no financiera)
* • Cumplimiento de las leyes y regulaciones aplicables
* UBA FCE –Auditoría y Seguridad de los Sistemas de Información Slide3
* COSO
* • Committee of Sponsoring Organizations of the Treadway Commission
* • Formado en 1985 para patrocinar la Comisión Nacional sobre Información Financiera Fraudulenta
* (Committee of Sponsoring Organizations of the Treadway Commission)

### Información Adicional

• Una iniciativa conjunta de cinco organizaciones del sector privado:
Misión: “Proveer de liderazgo de pensamiento a través de la elaboración de marcos y directrices globales
sobre la Gestión de Riesgo Empresarial, Control Interno y disuasión del fraude diseñado para
mejorar el desempeño organizacional y reducir el alcance del fraude en las organizaciones”
UBA FCE –Seguridad Informática y Principios de Auditoría Slide4
COSO – Control Interno
COSO Report o Informe COSO
Documento que orienta a las organizaciones sobre el control interno, gestión del riesgo, fraudes,
ética empresarial, presentación de informes financieros, entre otros.
Ha establecido un modelo común de control interno con el cual las organizaciones
pueden diseñar y evaluar sus sistemas de control.
UBA FCE –Seguridad Informática y Principios de Auditoría Slide5
Componentes COSO
Cubo de Control COSO 2013
Interno de COSO,
1992
Estructura de
la entidad
Componentes
“En los veinte años transcurridos desde la creación del marco original, los ambientes de negocio y operaciones
han cambiado drásticamente, volviéndose cada vez más complejos (tecnología, globalización). Al mismo
tiempo, las partes interesadas están más comprometidas, buscando una mayor transparencia y rendición de
cuentas para la integridad de los sistemas de control interno que apoyan las decisiones de negocios y el
gobierno de la organización”
UBA FCE –Auditoría y Seguridad de los Sistemas de Información Slide 6
Componentes COSO
Principios de
los conceptos
fundamentales
Componentes
17 Puntos de foco
Estructura de
que describen las
Principios
la entidad
características
de los principios
Puntos de foco
Control
Componentes
Componentes y principios son los requisitos para un sistema
eficaz de control interno
Puntos de foco y los controles están sujetos a juicio de la
administración
UBA FCE –Seguridad Informática y Principios de Auditoría Slide7
Componentes COSO
Ambiente/Entorno de Control
Establece un entorno en la organización que
estimula la concientización de su personal
respecto al control.
Los factores incluyen integridad, valores éticos,
competencia, autoridad, responsabilidad.
Base para todos los demás componentes de
control.
UBA FCE –Auditoría y Seguridad de los Sistemas de Información Slide 8
Componentes COSO
Ambiente de Control – principios
1. La organización demuestra un compromiso con la
integridad y los valores éticos.
2. El directorio demuestra independencia de la
administración y ejerce la supervisión del desarrollo y
desempeño de los controles internos.
3. La Gerencia establece, con supervisión del directorio,
estructuras, líneas de reporte y las autoridades y
responsabilidades apropiadas en la búsqueda de
objetivos.
4. La organización demuestra el compromiso de atraer,
desarrollar y retener a individuos competentes en la
alineación con los objetivos.
5. La organización mantiene individuos responsables de
sus compromisos de control interno en la búsqueda de
objetivos.
UBA FCE –Auditoría y Seguridad de los Sistemas de Información Slide 9
Componentes COSO
Evaluación de riesgos
Es la determinación y el análisis de riesgos
importantes para lograr los objetivos de la
entidad y formar la base para determinar las
actividades de control.
UBA FCE –Auditoría y Seguridad de los Sistemas de Información Slide 10
Componentes COSO
Evaluación de riesgos – principios
6. La organización especifica objetivos con claridad
suficiente para permitir la identificación y
evaluación de riesgos relacionados con los objetivos.
7. La organización identifica los riesgos para el logro
de sus objetivos a través de la entidad y analiza los
riesgos como base para la determinar cómo deben
administrarse.
8. La organización considera la posibilidad de fraude
en la evaluación de los riesgos para el logro de los
objetivos.
9. La organización identifica y evalúa los cambios que
podrían afectar significativamente el sistema de
control interno.
UBA FCE –Auditoría y Seguridad de los Sistemas de Información Slide 11
Componentes COSO
Actividades de Control
Políticas/procedimientos que aseguran que se
efectúan las directivas de la gerencia.
Gama de actividades que incluye aprobaciones,
autorizaciones, verificaciones,
recomendaciones, revisiones de rendimiento,
salvaguarda de activos y separación de
funciones.
UBA FCE –Auditoría y Seguridad de los Sistemas de Información Slide 12
Componentes COSO
Actividades de control – principios
10. La organización selecciona y desarrolla
actividades de control que contribuyan a la
mitigación de los riesgos (a niveles
aceptables) para el logro de sus objetivos.
11. La organización selecciona y desarrolla
actividades de control generales sobre la
tecnología para apoyar el logro de los
objetivos.
12. La organización implementa las actividades
de control a través de políticas que establecen
lo que se espera y procedimientos que ponen
las políticas en acción.
UBA FCE –Auditoría y Seguridad de los Sistemas de Información Slide 13
Componentes COSO
Información y Comunicación
Información pertinente determinada,
capturada y comunicada en forma oportuna.
Acceso a información generada interna y
externamente.
Flujo de información que permite acciones de
control exitosas desde instrucciones sobre
responsabilidades a resumen de observaciones
para acción de la gerencia
UBA FCE –Auditoría y Seguridad de los Sistemas de Información Slide 14
Componentes COSO
Información y comunicación – principios
13. La organización obtiene o genera y utiliza, la
información relevante y de calidad para apoyar el
funcionamiento de los controles internos.
14. La organización comunica internamente la
información, incluyendo los objetivos y las
responsabilidades de control interno, necesario
para apoyar el funcionamiento de los controles
internos.
15. La organización se comunica con las partes
externas con respecto a los asuntos que afectan el
funcionamiento de los controles internos.
UBA FCE –Auditoría y Seguridad de los Sistemas de Información Slide 15
Componentes COSO
Actividades de monitoreo
Evaluación del funcionamiento del sistema de
control en el tiempo.
Combinación de evaluación constante e
independiente.
Actividades de los niveles de supervisión y
gerencia.
Actividades de auditoría interna.
UBA FCE –Auditoría y Seguridad de los Sistemas de Información Slide 16
Componentes COSO
Actividades de monitoreo – principios
16. La organización selecciona, desarrolla y lleva a
cabo evaluaciones en curso y / o por separado para
determinar si los componentes del control interno
están presentes y funcionando.
17. La organización evalúa y comunica las deficiencias
de control interno en forma oportuna a las partes
responsables de tomar acciones correctivas,
incluyendo la alta dirección y el directorio, según el
caso.
UBA FCE –Auditoría y Seguridad de los Sistemas de Información Slide 17
Limitaciones de control interno
•Aplicación de juicio en las decisiones
“Ruptura” por fallas humanas (ej. errores)
Override por parte de la gerencia
Colusión
…además, como precondiciones, son necesarios procesos adecuados de governance en el directorio como así
también un efectivo seteo de objetivos organizacionales.
UBA FCE –Auditoría y Seguridad de los Sistemas de Información Slide 18

### Relación con otros temas
*   **Conexión con el marco general:** Este tema se integra con los conceptos de seguridad y auditoría del curso.

---


## 13. BCP

### Conceptos Principales

* Seguridad Informática y
* Principios de Auditoría
* Plan de Continuidad de Negocio (BCP)
* Plan de Recuperación ante Desastre (DRP) & Análisis de Impacto
* en el Negocio
* Profesor Pablo Gil
* Temario
* ➢ ¿Qué es BCP?
* ➢ Aspectos claves de BCP
* ➢ DRP & BIA

### Puntos Clave

* ➢ Principales amenazas a la continuidad de Negocio
* ➢ ¿Cómo armar un BCP?
* ➢ Estrategia del BCP
* ➢ Componentes del BCP
* ➢ Ejemplo práctico
* UBA FCE –Seguridad Informática y Principios de Auditoría Slide 2
* Algunos números interesantes…
* ➔ Un corte en la continuidad del negocio cuesta como
* desde USD 10.000 / hora (Fuente: Datto)
* ➔ 51% de las compañías no tienen Plan de Continuidad de

### Información Adicional

Negocio (BCP)
➔ 1 de cada 2 compañías experimentaron un corte
prolongado en sus operaciones
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 3
¿Qué es la continuidad de negocio? (BCP)
Es un concepto esencialmente proactivo, que busca evitar o mitigar el
impacto de un riesgo. Está formado por el BIA y el DRP
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 4
Aspectos claves del BCP
1. Alta disponibilidad: proporcionar la capacidad y los procesos para que una
empresa tenga acceso a las aplicaciones independientemente de las fallos
locales. Estos errores pueden producirse en los procesos de negocio, en los
recursos físicos o en el hardware o software de TI.
2. Operaciones continuas: proteger la capacidad de mantener las cosas en
funcionamiento durante una interrupción, así como durante las
interrupciones planificadas, como las copias de seguridad planificadas o el
mantenimiento planificado.
3. Recuperación tras desastre: establecer una manera de recuperar un
centro de datos en un sitio distinto si un desastre destruye el sitio primario o
lo deja inoperativo.
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 5
¿Que es el plan de recuperación ante desastres?
(DRP)
Es un concepto reactivo, que busca planear como recuperarse de un desastre y
restaurar el estado normal de las operaciones una vez que el riesgo se haya
presentado.
¿Qué es el Análisis de Impacto en el negocio? (BIA)
Identifica los efectos operativos y financieros de las interrupciones. Se deben
analizar los procesos de negocios, identificando aquellos criticos.
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 6
El RTO, o tiempo de recuperación objetivo. Tiempo máximo precisado
para restaurar sistemas y tenerlos de nuevo en un funcionamiento aceptable
El RPO, o punto de recuperación objetivo. Tiempo máximo que una
organización no puede permitir perder más información
MTD o MTPD, que es el período máximo tolerable de interrupción.
Tiempo máximo que un proceso puede estar caído sin grandes perjuicios para
la compañía
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 7
Identificas:
● Sistemas críticos que soportan los procesos y servicios del BCP
● Infraestructuras TIC en las que se soportan esos servicios
● Impacto por no disponer de las aplicaciones críticas
● Recursos necesarios para servicios TIC en el sitio alternativo (si procede)
● Identificaríamos prioridades RTO y RPO, pero para procesos de
instalación de componentes
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 8
Principales amenazas a la continuidad del negocio
A. Ciberataques / Pérdida de Información
B. Interrupción de los sistemas
C. Daños intangibles, reputacionales o de marcas
D. La pandemia / La crisis de salud / El COVID
E. Fallas para satisfacer las necesidades del cliente
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 9
¿Cómo armar un BCP?
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 10
Estrategia del BCP
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 11
Componentes de un BCP
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 12
Ejemplo de BCP - Gobierno de Colombia
(Año 2018)
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 13
Ejemplo de BCP - Gobierno de Colombia
(Año 2018)
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 14
Ejemplo de BCP - Gobierno de Colombia
(Año 2018)
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 15
Ejemplo de BCP - Gobierno de Colombia
(Año 2018)
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 16
Ejemplo de BCP - Gobierno de Colombia
(Año 2018)
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 17
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 18
Links - Bibliografia
https://www.adndatacenters.com/noticias/continuidad-negocio-vs-recuperacion-ante-desastres/
https://www.esap.edu.co/portal/wp-content/uploads/2019/03/Plan-de-continuidad-del-negocio-v1.pdf
https://www.linkedin.com/pulse/diario-del-ens-bia-bcp-y-drp-luis-andr%C3%A9s-
var%C3%B3n/?originalSubdomain=es
https://www.ibm.com/es-es/services/business-continuity/plan
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 19

### Relación con otros temas
*   **Conexión con el marco general:** Este tema se integra con los conceptos de seguridad y auditoría del curso.

---


## 14. ITGC en detalle

### Conceptos Principales

* Seguridad Informática y Principios de
* Auditoría
* Controles Generales de TI (ITGC)
* Profesor Pablo Gil
* Controles Generales de TI (CGTI)
* EEFF
* Aserciones
* financieras
* Operación del negocio Reporte
* Principales clases de transacciones, procesos y subprocesos de negocio financiero

### Puntos Clave

* •Exactitud
* ) •Integridad
* s Riesgos asociados al procesamiento de transacciones
* t •Corte
* r •Existencia y
* i Consideraciones del proceso
* n ocurrencia
* d •Derechos y
* Controles a nivel de entidad (directos)
* obligaciones

### Información Adicional

n •Presentación y
e Controles a nivel de transacción
divulgación
Procedimientos y Controles manuales
i n controles automáticos dependientes de IT Controles manuales •Valuación
o Controles generales de TI (CGTI)
Riesgos asociados al uso de sistemas Segregación
de funciones
Ambiente de TI
Aplicaciones e
Datos
infraestructura
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 2
Controles Generales de TI (CGTI)
Políticas, procedimientos y actividades de control que
se relacionan con varios sistemas/aplicaciones y
t soportan el funcionamiento efectivo de los controles a
i nivel de transacción.
e Controles a nivel de transacción
Procedimientos y Controles manuales
Controles manuales
i n controles automáticos dependientes de IT
o Controles generales de TI (CGTI)
Riesgos asociados al uso de sistemas
Ambiente de TI
Aplicaciones e
Datos
infraestructura
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 3
Controles Generales de TI (CGTI)
Los CGTI son relevantes ya que constituyen el ambiente de generación
y procesamiento de:
• Cálculos realizados por las aplicaciones
• Reportes generados por las aplicaciones
• Controles automáticos
• Seguridad lógica (incluyendo segregación de funciones)
• Interfases entre aplicaciones
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 4
Controles Generales de TI (CGTI)
Riesgos que surgen del ambiente de TI (agrupados en 4 dominios)
Acceso al software y a los datos
• Accesos no autorizados a las aplicaciones por parte de usuarios de negocio
• Accesos no autorizados a las aplicaciones por parte de usuarios de TI
• Cambios no autorizados a los datos
Cambios a los programas
• Cambios no solicitados por el negocio
• Cambios que introducen errores en las aplicaciones
• Cambios directos y no autorizados a los programas
• Cambios directos y no autorizados a la configuración del sistema
• Actualizaciones inadecuadas de sistemas operativos y bases de datos
Desarrollo de sistemas
• Los sistemas implementados procesan datos de manera incorrecta debido a problemas de
codificación o configuración
• Errores al migrar registros de transacciones y/o datos maestros
Operaciones computarizadas
• Fallas en los sistemas que causan pérdida de datos o transacciones o incapacidad para
acceder a ellos según se requiera
• Intervención manual inapropiada o fallas en el procesamiento de trabajos programados
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 5
Dominio:
Accesos a Programas y Datos
Riesgos:
Accesos no autorizados a las aplicaciones por parte de usuarios de
negocio
Accesos no autorizados a las aplicaciones por parte de usuarios de TI
Cambios no autorizados a los datos
Controles Generales de TI (CGTI)
Acceso al software y a los datos
Administración y monitoreo de la seguridad
u Datos Aplicaciones
r Sistema operativo
e Red interna
Red perimetral
Seguridad física
Administración de accesos – usuarios de negocio
Administración de accesos – usuarios técnicos
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 7
Controles Generales de TI (CGTI)
Acceso al software y a los datos – Seguridad de la aplicación
Aplicación Datos de
negocio
Usuarios de
negocio
Superusuarios
La organización debe asegurar que:
- Sólo los usuarios de negocio autorizados tienen acceso a las aplicaciones
- Los niveles de acceso de los usuarios dentro de las aplicaciones son
apropiados y no contradicen la segregación de funciones
- Las cuentas privilegiadas (o superusuarios) dentro de las aplicaciones son
controladas
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 8
Controles Generales de TI (CGTI)
Acceso al software y a los datos – Seguridad de la aplicación
Las empresas necesitan saber quién tiene acceso a
cada función en las aplicaciones, y asegurarse de que
no se otorguen privilegios inapropiados
Controles Preventivos Controles Detectivos
 Diseñar roles adecuados  Analizar roles y responsabilidades de
usuarios para conflictos SoD
 Proveer un adecuado proceso de
aprovisionamiento de usuarios  Identicar y corregir conflictos SoD
 Aplicar controles compensatorios  Monitorear actividad de usuarios y/o
cuando el acceso no puede ser evitado funciones críticas
 Restringir y controlar los accesos de
contratistas/consultores
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 9
Controles Generales de TI (CGTI)
Acceso al software y a los datos – Seguridad de los datos
Aplicación Datos de
negocio
Usuarios de
negocio
Usuarios de
IT (DBA)
La seguridad de datos se refiere a:
- Seguridad sobre bases de datos, archivos y/o conjuntos de datos
- Controles sobre el acceso directo a los datos utilizando funciones
especiales
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 10
Controles Generales de TI (CGTI)
Acceso al software y a los datos – Seguridad de los datos
¿Cómo se controla?:
• Revisando y autorizando debidamente las solicitudes de acceso
a las bases de datos y archivos
• Removiendo de forma oportuna los accesos del personal
desvinculado
• Monitoreando de forma periódica las transacciones y
actividades de los superusuarios y/o usuarios administradores
• Aplicando estándares robustos de seguridad y contraseñas
adecuadas a las bases de datos y archivos
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 11
Controles Generales de TI (CGTI)
Acceso al software y a los datos – Seguridad del Sistema
operativo
• Las aplicaciones, bases de datos y software de red residen en
hardware/infraestructura
• Cada servidor o instancia posee su propio sistema operativo,
aunque la seguridad puede controlarse a través de funcionalidad
común de seguridad (ej. controladores de dominio)
• Los sistemas operativos poseen ciertas cuentas privilegiadas que
pueden otorgar accesos amplios a aplicaciones y datos
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 12
Controles Generales de TI (CGTI)
Acceso al software y a los datos – Seguridad del Sistema
operativo
¿Cómo se controla?:
• Restringiendo el acceso a cuentas privilegiadas solamente al
personal apropiado
• Monitoreando de forma periódica la actividad de los
superusuarios y/o usuarios administradores
• Aplicando estándares robustos de seguridad y contraseñas
adecuadas a las sistemas operativos
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 13
Controles Generales de TI (CGTI)
Acceso al software y a los datos – Administración de accesos
de usuarios de negocio
Los accesos son requeridos por
personal autorizado
Los accesos son otorgados por una
función separada en TI
Existen revisiones periódicas de los
accesos vigentes de usuarios
Los accesos del personal desvinculado
son removidos oportunamente
Los incidentes de seguridad son
identificados e investigados
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 14
Controles Generales de TI (CGTI)
Acceso al software y a los datos – Administración de accesos
de usuarios técnicos
• Todas las aplicaciones, sistemas operativos y bases de datos (también
otros componentes de TI tales como firewalls) poseen un
“administrador” o cuentas privilegiadas:
- Windows = Administrador de dominio
- UNIX = Root
- OS/400 = QSECOFR
- Base de datos SQL Server = DBA
- Base de datos Oracle = SYS, SYSTEM
• Se requieren medidas de seguridad adicionales (por ejemplo
ensobrado de claves – hoy con software) y asignación específica al
personal que desempeña la función.
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 15
Dominio:
Desarrollo y Cambios a los programas
Riesgos:
- Cambios no solicitados por el negocio
- Cambios que introducen errores en las aplicaciones
- Cambios directos y no autorizado a los programas
- Cambios directos y no autorizados a la configuración del sistema
- Actualizaciones inadecuadas de sistemas operativos y bases de datos
- Los sistemas implementados procesan datos de manera incorrecta
debido a problemas de codificación o configuración / desarrollos no
autorizados
- Errores al migrar registros de transacciones y/o datos maestros
Ambientes de procesamiento – separación de
entornos o ambientes
Prueba /
Desarrollo Producción
Nota: la separación de ambientes puede ser física o lógica
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 17
Desarrollo de sistemas
s m s s
o i a
i a t r
Naturaleza del cambio
b r e r
m g m o
a a l
Actualizaciones Pequeñas Actualización de Nueva
Arreglos
menores mejoras la aplicación implementación
(versions (Sistema /
antiguas o Modulo)
desactualizadas)
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 18
Desarrollo de sistemas
Ciclo de vida tradicional del desarrollo de sistemas
Inicio del
proyecto
Análisis y
Implementación diseño
Conversión de
datos Construcción
Prueba y QA
Segregación de funciones
Administración de proyectos
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 19
Metodologías ágiles
Marco de trabajo - SCRUM
Principales roles Principales documentos
✓ Scrum Master:hace cumplir • ProductBacklog:requisitos de
el marco de trabajo Scrum alto nivel que definen el trabajo
✓ ProductOwner (negocio): a realizar
representa a los stakeholders • Sprint Backlog:requisitos a
✓ DevelopmentTeam:equipo desarrollar en el siguiente sprint
de desarrollo • Definition of Done:documento
que determina que tarea realizada
Flujo de Trabajo
o Sprint Planning:evento de planificación al inicio del sprint
o Daily Scrum:reunión diaria de status del proyecto
o Sprint Review:al final el sprint se realiza una revisión y retrospectiva del desarrollo
o Sprint Retro:al final el sprint se realiza reunión de mejora continua del proceso
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 20
Cambios a los sistemas
Las organizaciones necesitan contar con un proceso
de desarrollo y/o cambios a los programas definido y
formalizado
Controles Preventivos / Detectivos
 Contar con una adecuada separación de ambientes (DEV-QA-PRD)
 Contar con una adecuada segregación de funciones (personal de desarrollo no
accede a producción)
 Verificar que los desarrollos/cambios fueron debidamente solicitados y
especificados (ej: doc. con diseño funcional del cambio)
 Verificar que los desarrollos/cambios fueron probados en ambiente de prueba
previo a su pasaje en producción (user acceptance testing)
 Verificar que los desarrollos/cambios estén debidamente autorizados previo a su
pasaje a producción
 Verificar que las migraciones o conversiones de datos fueron debidamente
probadas y documentadas previo a la puesta en producción de un nuevo sistema
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 21
Dominio:
Operaciones computarizadas
Riesgos:
- Fallas en los sistemas que causa pérdida de datos/transacciones o
incapacidad para acceder a ellos según se requiera
- Intervención manual inapropiada o fallas en el procesamiento de
trabajos programados
Controles generales de TI
Operaciones computarizadas
¿Por que las operaciones computarizadas de TI son
importantes?
• Asegurar que los sistemas que soportan los procesos
principales del negocio están funcionando de manera
correcta y garantizan que los datos se almacenan, conservan y
transfieren de manera íntegra y exacta.
• Para hacer un seguimiento eficiente de las actividades de
procesamiento de información, backups, gestión de
problemas, entre otros.
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 23
Controles generales de TI
Operaciones computarizadas
Procesamiento automático de información
n s Actividades de procesamiento de transacciones
t a Administración y resolución de problemas
d e Operaciones del centro de cómputos
Actividades tendientes a asegurar que los datos de los sistemas productivos
son procesados de forma íntegra y exacta, de acuerdo a los objetivos de la
organización, y que los problemas de procesamiento son identificados y
resueltos oportunamente
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 24
Controles generales de TI
Operaciones computarizadas
Actividades de procesamiento de transacciones
• Programación y monitoreo de procesos batch (lotes)
• Procesamiento en tiempo real
• Procesamiento de interfaces
• Monitoreo del procesamiento de transacciones
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 25
Controles generales de TI
Operaciones computarizadas
Administración y resolución de problemas
• Identificación de problemas
• Monitoreo de la red
• Funciones de mesa de ayuda (niveles 1 a 3)
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 26
Controles generales de TI
Operaciones computarizadas
Operaciones del centro de cómputos
• Backups y recupero de información
• Plan de recuperación ante desastres y/o plan de continuidad de negocio
• Protección ambiental
• Gestión de la capacidad
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 27
Controles generales de TI
Operaciones computarizadas
Puntos de foco de los controles
a) Controles gerenciales
- Políticas normas y procedimientos relacionados con monitoreo de
procesos
- Definición de roles y responsabilidades – SoD y Accesos críticos
- Controles de monitoreo
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 28
Controles generales de TI
Operaciones computarizadas
Puntos de foco de los controles
b) Procesamiento batch
- Proceso de alta baja y modificación de procesos batch / jobs
- Proceso de recuperación de cada job
- Monitoreo de procesos
- Controles de acceso sobre la herramienta de seguimiento de jobs.
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 29
Controles generales de TI
Operaciones computarizadas
Puntos de foco de los controles
c) Procesamiento en tiempo real
- Configuración de procesamiento en tiempo real (transacciones
inter módulo, middleware)
- Cómo se capturan y qué alertas generan los errores en el
procesamiento en tiempo real
- Accesos a los cambios de configuración
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 30
Controles generales de TI
Operaciones computarizadas
Puntos de foco de los controles
d) Backups y administración de problemas
- El contenido y la frecuencia de los backups versus objetivos del
negocio
- Cómo se asegura la compañía que los backups estarán disponibles
en caso de una emergencia
- Proceso de restore de información
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 31

### Relación con otros temas
*   **Conexión con el marco general:** Este tema se integra con los conceptos de seguridad y auditoría del curso.

---


## 15. Procesos de Negocio

### Conceptos Principales

* Seguridad Informática y Principios
* de Auditoría
* Riesgos y controles en Procesos de Negocio /
* Accesos y segregación de funciones
* Profesor Pablo Gil
* Controles de aplicación (procesos de negocio)
* EEFF
* Aserciones
* financieras
* Operación del negocio Reporte

### Puntos Clave

* Principales clases de transacciones, procesos y subprocesos de negocio financiero
* • Exactitud
* ) • Integridad
* s Riesgos asociados al procesamiento de transacciones
* t • Corte
* r • Existencia y
* i Consideraciones del proceso
* n ocurrencia
* • Derechos y
* Controles a nivel de entidad (directos)

### Información Adicional

obligaciones
n • Presentación y
e Controles a nivel de transacción
divulgación
Procedimientos y Controles manuales
i Controles manuales • Valuación
n controles automáticos dependientes de IT
o Controles generales de TI (CGTI)
Riesgos asociados al uso de sistemas Segregación
de funciones
Ambiente de TI
Aplicaciones e
Datos
infraestructura
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 2
Controles de aplicación
Controles a nivel de transacción o aplicación que se encuentran
operando en los procesos y subprocesos de negocio.
• Actividades de control manuales o automáticas
• Pueden ser preventivos o detectivos
• Operan a un nivel detallado de proceso de negocio
• Están diseñados para asegurar la integridad de los registros contables
• Soportan directamente los objetivos de procesamiento de la
información (Integridad, Exactitud, Validez, Acceso Restringido)
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 3
Ciclo de Abastecimiento
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 4
Subprocesos del ciclo de C / CxP / Pagos
Procesamiento de órdenes de compra
Recepciones de bienes/servicios o
Procesamiento de documentos de proveedor m
Pagos t
Ajustes y cierre contable
UBA FCE –Auditoría y Seguridad de los Sistemas deInformación
Slide5
Datos Maestros
Maestro de
Productos
Maestro de
/servicios Maestro de Bancos
Proveedores
Datos Maestros
Controles ABM datos maestros + análisis de proveedores
Riesgos
- Altas, bajas o modificaciones de datos maestros de forma incompleta, incorrecta y/o
duplicada
- Utilización de datos maestros no actualizados que causarán errores en la información
procesada por transacciones
UBA FCE –Auditoría y Seguridad de los Sistemas deInformación Slide6
Procesamiento de Ordenes de Compra
Ordenes de
Captura de las
compra de Monitoreo de
necesidades del
bienes y compras
negocio
servicios
Principales actividades Riesgos Controles
•Solicitud y Aprobación de necesidades de
Emisión de pedidos sobre bienes/servicios no
servicios, materia prima, activo fijo, materiales, Aprobación de requisiciones/solicitud de pedido
requeridos
etc (requisiciones de compra)
• Proceso de cotizaciones, selección de Procesamiento de ordenes de compra / contratos Reconciliación entre requisiciones y ordenes de
proveedor no autorizados compra.
•Autorización y emisión de pedido/orden de Matching entre los datos de la OC y el maestro de
compra proveedores.
• Monitoreo de ordenes de compra pendientes Aprobación de la ordenes de compra.
UBA FCE –Auditoría y Seguridad de los Sistemas deInformación
Slide7
Recepciones
Recepción de
Actualización
bienes o Contabilización
de inventarios
servicios
Principales actividades Riesgos Controles
Las cantidades registradas en el sistema de
Reconciliación entre las cantidades recibidas vs.
•Recepción de bienes vs orden de compra inventarios no coinciden con las cantidades
Remitos del proveedor
físicas recibidas.
Las recepciones no son registradas en el período Actualización de stock una vez ingresada la
• Aceptación de los servicios recibidos
correspondiente mercadería
•Registro de las recepciones en la contabilidad
Los servicios certificados no fueron prestados Autorización de servicios recibidos
(impacto en inventarios)
Imposibilidad de recepcionar cantidades por
• Provisión de facturas a recibir
sobre las detalladas en la OC
Administración de tolerancias en cantidades
UBA FCE –Auditoría y Seguridad de los Sistemas deInformación
Slide8
Procesamiento de documentos de proveedor
Procesamiento
Ingreso de
y validación Contabilización
facturas
facturas
Principales actividades Riesgos Controles
•Procesamiento de documentos de proveedor Las facturas no son procesadas o se procesan 3W Matching (Automático o Manual) Factura vs
(ingreso en el sistema) con datos erróneos. Recepción Vs Orden de compra
Revisión manual sobre la exactitud (monto de la
• Procesamiento de facturas sin orden de compra Existencia de facturas ficticias.
factura)
•Resolución de diferencias (diferencias entre Las facturas son registradas en el período
Control de duplicidad de documentos
facturas y órdenes de compra) incorrecto.
• Imputaciones en las cuentas contables de
Facturas duplicadas. Autorización de facturas sin OC
proveedores
UBA FCE –Auditoría y Seguridad de los Sistemas deInformación
Slide9
Pagos
Generación de
Generación de
Transferencias
Ordenes de Contabilización
bancarias o
Pago
cheques
Principales actividades Riesgos Controles
•Selección de facturas basadas en los términos Restricción para incluir una factura más de una
Pagos no autorizados.
de pago vez en una propuesta de pago
Pagos que no están relacionados al pasivo de la
•Confección de órdenes de pago Autorización de órdenes de pago
compañía.
•Custodia/confección de los cheques Pagos no registrados en el período correcto. Control entre facturas a pagar versus pagos
•Pagos a través de transferencias o cartas de
Pagos no registrados en la contabilidad. Autorización cheques emitidos a proveedores
instrucción
Pagos registrados en el sistema vs. Pagos
•Otros pagos (ej sueldos, impuestos)
efectuados en sistemas de cash management.
•Contabilización de los pagos Autorizaciones de transferencias
Reconciliación con extracto bancario
UBA FCE –Auditoría y Seguridad de los Sistemas deInformación
Slide10
Ajustes y cierre contable
Contabilización Preparación de Contabilización
en los mayores ajustes de ajustes
Principales actividades Riesgos Controles
•Conciliaciones entre cuentas auxiliares (Sub- Previsiones o provisiones calculadas de manera
Cálculo y revisión de provisión de facturas a recibir
diarios) de proveedores y libro mayor incorrecta
• Confección de provisiones de gastos y
Omisión de pasivos Cálculo y revisión de provisiones de gastos
contabilización
Saldos de cuentas a pagar no son registrados en el
Aprobación de ajustes contables
tipo de cambio correcto
Ajustes ficticios o erróneos
UBA FCE –Auditoría y Seguridad de los Sistemas deInformación
Slide11
Consideraciones de fraude
• Pagos no autorizados
• Proveedores fraudulentos
• Cheques inutilizados
• Falta de segregación de funciones y revisión por parte del
management
• Accesos a ejecutar pagos por personal no autorizados
• Proveedores inexistentes
• Proceso de selección de proveedores
UBA FCE –Auditoría y Seguridad de los Sistemas deInformación
Slide12
Ciclo de Ingresos
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 13
Subprocesos del Ciclo de Ventas/Cuentas a Cobrar/Cobranzas
Pedidos
Envíos Mercaderías
Facturación
Devoluciones
Cobranzas
Descuentos D
Incobrables
Cierre contable
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 14
Datos Maestros
Maestro de Precios Contratos
Maestro de
Maestro de Clientes
Datos Maestros
productos
Controles ABM datos maestros + analisis de clientes
Riesgos:
- Altas, bajas o modificaciones de datos maestros de forma incompleta, incorrecta y/o duplicada
- Utilización de datos maestros no actualizados que causarán errores en la información procesada
por transacciones
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 15
Generación de Pedidos
Monitoreo de
Requerimientos Creación de
pedidos de
de clientes Orden
venta
UBA FCE –Auditoría y Seguridad de los Sistemas deInformación
Slide16
P r in c ip a le s a c t iv id a d
G e n e r a c ió n d e C o t iz a c ió n / O f e r t
e b id a m e n t e a u t o r iz a d a .
R e c e p c ió n d e r e q u e r im ie n t o s d e
In g r e s o d e P e d id o s d e V e n t a .
V e r if ic a c ió n d e c r é d it o .
C o n f ir m a c ió n d e P e d id o s d e V e n
M o n it o r e o y a n á lis is d e p e d id o s p
a d e v e
c lie n t e
t a a c lie
e n d ie n
n t a
n t e s
t e s .
ió n
e s a
e s a
le m
R ie
n o a u
p lic a d
p e d id
io s
s g o s
t o r iz a d
o d e p e
o s d e c
d id
lie n
e s c o n
u t o r iz a c ió n d e la s c o t
a lid a c ió n d e d a t o s e n
e n t a ( e j. : c a m p o s c la v
e a liz a r la v a lid a c ió n d
la e m is io n d e l p e d id o
n t r o le s
iz a c io n e
la c a r g a
e s )
e l c o n t r o
d e v e n t a
o f e
r t a
v io
Facturación y Envíos de Mercaderías
Ingresar orden Crear factura y
y crear pedido enviar Contabilización
de cliente mercaderias
Principales actividades Riesgos Controles
•Requerimiento con Orden de compra manual o Generar facturas de forma automática tomando
Facturación incompleta, incorrecta y/o duplicada
electrónica recibida del cliente (validación datos) los datos claves del Pedido
Precio en la factura en comparación con el precio
Errores en la contabilización de la factura (cuenta
• Pedido de cliente creado maestro, la cantidad en comparación con los
contable, cuenta corriente, etc.)
documentos de envío
• Productos recogidos, envasados y enviados Imposibilidad de modificar campos críticos en el
(actualización stock) documento factura
Factura creada y contabilizada en el libro auxiliar
• Generación de facturas y contabilización
de ventas
UBA FCE –Auditoría y Seguridad de los Sistemas deInformación
Slide17
Devoluciones - Generación de Notas de Crédito)
Crear
Preparar y
formulario de
registrar ajuste Contabilización
devolucion de
de crédito (NC)
ventas
UBA FCE –Auditoría y Seguridad de los Sistemas deInformación
Slide18
P r in c ip a le s a c t iv id a d e s
• R e c ib ir y p r o c e s a r s o lic it u d d e d e v o lu c ió n d e
b ie n e s o s e r v ic io s
• A p r o b a c ió n d e s o lic it u d e s d e d e v o lu c ió n
• E n v ío d e la a u t o r iz a c ió n d e d e v o lu c ió n y
r e c e p c ió n d e lo s b ie n e s
• E l p r e c io d e la d e v o lu c ió n d e b e s e r ig u a l a l
im p o r t e o r ig in a l r e g is t r a d o
• G e n e r a c ió n y c o n t a b iliz a c ió n d e d e v o lu c io n e s
e r m it ir d e v o lu c io n
n o d e v o lu c ió n ” o f u
m is ió n d e n o t a s d e
u t o r iz a d a o p o r m o
m is ió n d e n o t a s d e
e r c a d e r ía
r r o r e s e n la c o n t a b
o n t a b le , c u e n t a c o
R ie s g o s
e s a c lie n t e s c o
e r a d e la p o lít ic
c r é d it o d e f o r m
n t o s in c o r r e c t o
c r é d it o s in h a b
iliz a c ió n d e la f
r r ie n t e , e t c . )
n a c u e r d o
a d e f in id a
a n o
e r r e c ib id
a c t u r a ( c u
n t a
o lo s
ie n e
o lít ic
o d a s
l c r é
e v u e
l c r é
o n t a
e p e r
s o s e
la s N
d it o s
lt a s s
d it o d
b iliz a
C o n t r o le s
m it e n r e c ib ir y p r o c e s a
r v ic io s d e a c u e r d o a lo
C s o n d e b id a m e n t e a u
e e m it e d e s p u é s d e q u
o n r e c ib id a s
e d e v o lu c ió n d e v e n t a s
e n e l L ib r o m a y o r .
r d e v o lu
d e f in id o
t o r iz a d a
e la s m e
s e c r e a
c ió
r c a
ía s
Cobranzas
Recibir Aplicar
cobranzas de cobranzas a Contabilización
clientes facturas
UBA FCE –Auditoría y Seguridad de los Sistemas deInformación
Slide19
P r in c ip a le s a c t iv id a d e s
S e in g r e s a n lo s r e c ib o s p o r la s c o b r a n z a s
e c ib id a s e n la c u e n t a c o r r ie n t e d e lo s c lie n
S e r e a liz a n c o n t r o le s s e g ú n m é t o d o s d e
o b r a n z a s
S e lle v a n a c a b o p r o c e d im ie n t o s b a n c a r io
o n t r o l d e d in e r o r e c ib id o
G e n e r a c ió n y c o n t a b iliz a c ió n d e c o b r a n z a
C o b r a n z a s q u e n o s e p u e d e id e n t if ic a r s o n
u e s t a s e n s u s p e n s o
t e s
o c e s
lic a c
g is t r
ie n t o
n d e c
e c o b
d u p
o b r a
r a n z
s g o
is t
b r a
t e s
n t e
o r r e c t o s
S e in g r e s a n lo
r e c ib id a s
L a c o b r a n z a s
la f a c t u r a c o r
C o b r a n z a s q u
p u e s t a s e n s u
C o n t r o le s
e c ib o s p o r t o
p lic a n c o n t a b
t a ( s i s e c o n o
o s e p u e d e id
e n s o
d a s la s c
iliz a n e n
c e e l d a t
e n t if ic a r
o b r
e l c
s o n
t e y
Gestión de cuentas a cobrar (incobrables)
Preparar
Control de
entrada de
estimaciones Contabilización
deudas
contables
incobrables
UBA FCE –Auditoría y Seguridad de los Sistemas deInformación
Slide20
r in
le s a
u t in a
e s p e
c t iv id
r ia m
c íf ic
c ió n
r é d it o s a n
ig ü
g o s
s n o r e c u p e r a b le s o L a
s / e s t im a
e c o n t a b iliz a n e n e l
Ajustes y cierre contable
Cerrar el libro
auxiliar, publicar Preparación de Contabilización
en el libro ajustes de ajustes
mayor
UBA FCE –Auditoría y Seguridad de los Sistemas deInformación
Slide21
r e p
r o d
r io
r lo
P r in c ip a
u b lic a r la
in t e r f a s e
s a ju s t e s
c t iv id a d e
r a d a d e l s
t r e s is t e m
la d o c u m
u b - d
a s )
e n t a
r io
ió n
r r o
r e s
is ió
t a s
p le
n t a
R ie s g o s
n t a b iliz a c ió n e n e l lib
o n t a b iliz a c ió n d e v e n
b iliz a d a s d e f o r m a e r
lo s p r in c ip io s c o n t a b
q u e n o
e s c
ilia c
ió n
b ia
r io
n t r
r o s
r io
o le
j p r
is io n
s d ia
r io
Accesos y segregación de funciones
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 22
Accesos a los datos
Datos
Aplicaciones
Sistemas operativos (BD / File Servers)
Accesos
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 23
Accesos
Riesgos
• Accesos no autorizados a las aplicaciones por parte de usuarios de negocio o accesos que no se
corresponden con su función
• Accesos no autorizados a las aplicaciones y a los datos por parte de usuarios de TI
• Cambios no autorizados a los datos
Controles
• Diseñar e implementar un proceso adecuado de aprovisionamiento de usuarios/permisos
• Implementar adecuados controles sobre los usuarios sensitivos/superusuarios de BD y SO
• Definir los accesos que son considerados críticos a nivel de los distintos procesos de negocio
• Realizar certificaciones / reválidas de usuarios y permisos
• Utilizar software específico de monitoreo de accesos críticos
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 24
Accesos críticos – ejemplos por proceso
PROCESO ACCESOS
Ventas Modificar límite de crédito /ABM precios / Ingresar facturas / Emitir notas
de crédito o débito / Anular documentos de clientes
Compras ABM proveedores / Modificar orden de compra / Aprobar orden de compra
/ Gestionar movimientos de stock / Registrar diferencias inventario
Cuentas a Pagar Ingresar o aprobar documentos de proveedor sin OC / Anular documentos
de proveedores
Pagos Autorizar pagos / Ingresar y aprobar anticipos a proveedores / Anular pagos
/ Administrar fondo fijo
Payroll ABM empleados / Ingresar y autorizar novedades de liquidación / Modificar
liquidación
Activo Fijo Procesar baja de activo fijo / Registrar transferencias
Contabilidad ABM cuentas contables / Administración tipo cambio / Administración
período contable / Ingreso asientos manuales
Seguridad IT Crear usuarios / Asignar roles a usuarios / Modificar parámetros Seguridad
/ Acceso y administración tablas / Restablecer contraseñas
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 25
Segregación de funciones
Segregación de funciones
• Un sistema efectivo de control interno requiere una correcta división de responsabilidades.
Idealmente el trabajo de una persona debería ser independiente o servir como revisión del
trabajo de otra persona.
• La segregación de funciones:
✓ Reduce el riesgo de un error no detectado y limita las oportunidades de apropiación
indebida de activos o de ocultar declaraciones intencionalmente equivocadas en los
EEFF.
✓ Actúa como control disuasivo del fraude o encubrimiento de operaciones debido a la
necesidad de connivencia.
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 26
Segregación de funciones
Una combinación de funciones es considerada incompatible si, siendo asignada a una sola
persona, esta puede, eventualmente, cometer errores y/o llevar a cabo irregularidades en el
curso de sus tareas diarias.
Ejemplos:
• Crear ordenes de compra vs. Facturar/Pagar
• Crear cuenta contable vs. Ingresar asiento
• Vender vs. Modificar los % de comisión
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 27
Segregación de funciones
Una adecuada segregación de funciones es difícil de lograr:
• Falta de recursos / Compañías con una reducida estructura organizativa
• Costos
• Problemas prácticos / operativos
Cuando no es posible establecer una adecuada segregación de funciones, la Gerencia debería
diseñar/implementar controles compensatorios para obtener un nivel similar de satisfacción
de control.
Slide 28
UBA FCE –Seguridad Informática y Principios de Auditoría
Segregación de funciones
Matriz de segregación de funciones – ejemplos Compras/Pagos
s a r p m a r r o
r o d e e v o r P M D r a la ir e t a M M D r a r p m o C M D s o r t O r a o d id e P e d d u t ic ilo S o d id e P e d d u t ic ilo r o d e e v o r P r a o C e d o t n e m u c o D p m o C e d o t n e m u c o n ó ic p e c e R d e e v o r P e d a r u t c a F a d a e u q o lB a r u t c a o g a P s e u q e h C r a
r t s in
im d A
r t s in
im d A
r t s in
im d A
r e n e G
a r e b iL
n im
r e t e D
r a s
e c o r P
a r e b iL
r a s
e c o r P
c if ir e V
a r e b iL
r a s
e c o r P
r t s in
im d A
Administrar DM Proveedor n x x x x x x
Administrar DM Material n n x x
Administrar Otros DM Compras n n n
Generar Solicitud de Pedido n n n n x x
Liberar Solicitud de Pedido n n n n n
Determinar Proveedor n n n n n n
Procesar Documento de Compra n n n n n n n x x x x
Liberar Documento de Compra n n n n n n n n
Procesar Recepción n n n n n n n n n x x
Verificar Factura de Proveedor n n n n n n n n n n x x x
Liberar Factura Bloqueada n n n n n n n n n n n x x
Procesar Pago n n n n n n n n n n n n
Administrar Cheques n n n n n n n n n n n n n
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 29
Segregación de funciones
Ejemplo controles compensatorios
Incompatibilidad Control Manual dep IT
Revisión periódica de facturas
Administrar datos
emitidas a clientes creados o
maestros de clientes
modificados por el mismo usuario
Emitir facturas
Control Automático
Imposibilidad de emitir facturas para
clientes creados o modificados por el
mismo usuario, o bloqueo y solicitud
de liberación a través del sistema.
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 30
Segregación de funciones
Consideraciones técnicas
A nivel rol/perfil
Entre roles/perfiles asignados a un usuario en
un sistema
Segragación de funciones inter-sistemas
Segragación de funciones inter-sistemas +
aplicaciones de terceros (ej Interbanking)
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 31
Segregación de funciones
Controles
• Verificar la segregación de funciones al momento de crear y/o asignar un rol o perfil
• Realizar evaluaciones periódicas de segregación de funciones
• Realizar certificaciones / reválidas de usuarios y permisos
Es necesario utilizar software específico de evaluación de accesos y
segregación de funciones
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 32

### Relación con otros temas
*   **Conexión con el marco general:** Este tema se integra con los conceptos de seguridad y auditoría del curso.

---


## 16. Normas Auditoría

### Conceptos Principales

* Seguridad Informática y Principios de
* Auditoría
* Normas de auditoría
* Profesor Pablo Gil
* RT 53 – Normas de Auditoría, Revisión, Otros Encargos de
* Aseguramiento, Certificaciones, Servicios Relacionados e
* Informes de Cumplimiento– FACPCE
* La resolución técnica tiene por
* objeto regular la condición básica
* para el ejercicio de la auditoría de

### Puntos Clave

* estados contables, la revisión de
* estados contables de períodos
* intermedios, otros encargos de
* aseguramiento, certificaciones y
* servicios relacionados e informes de
* cumplimiento
* La RT modificó a la RT 37 en sept 2021
* https://www.ambito.com/novedades-fiscales/novedades-fiscales/los-cambios-punto-
* punto-las-normas-auditoria-n5309478
* UBA FCE –Seguridad Informática y Principios de Auditoría Slide 2

### Información Adicional

Etapas de la auditoría
Planificación Ejecución Conclusión
Auditoría de sistemas/procesos/control interno
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 3
RT 53
Independencia
El contador público debe tener independencia con relación al ente al que se
refiere la información objeto del encargo.
La actitud mental independiente y la independencia aparente son necesarias
Aspectos a tener en cuenta:
- Relación de dependencia con el ente o con los entes que estén vinculados
económicamente a aquel
- Relación de parentesco (cónyuges, pariente por consanguinidad o afinidad)
- Socio, asociado, director o administrador del ente
- Intereses significativos en el ente o en entes relacionados
- Remuneración dependiente de las conclusiones
- Remuneración contingente, dependiente de las conclusiones o pactada en base a
resultados del período o variables similares
- Aplicación de requisitos de independencia a todo el equipo de trabajo
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 4
RT 53
Aspectos a tener en cuenta para el desarrollo del encargo
- Elementos de juicio válidos y suficientes que permitan respaldar las
aseveraciones formuladas en el informe
- Documentación del encargo
▪ Programas de trabajo formalizados
▪ Papeles de trabajo: descripción de la tarea, datos y antecedentes (preparados por el
contador o recibido de terceros)
▪ Conclusiones particulares y generales
▪ Plazo de conservación: el que fijen las normas legales o 10 años
- Principio de economía: lapso y costo razonables
- Consideraciones sobre significación/materialidad y riesgos
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 5
RT 53
Aspectos a tener en cuenta para el desarrollo del encargo
- Procedimientos pueden realizarse sobre bases selectivas (muestras) y
utilizando métodos estadísticos
- Obtención de manifestaciones escritas de parte de la dirección
- Modificación de procedimientos: puede darse si es factible demostrar
que el procedimiento usual no fue practicable o que a pesar de la
modificación se reunieron elementos de juicio válidos y suficientes
- Aplicación de procedimientos en revisión de operaciones o hechos
posteriores al cierre
- Utilización de trabajo de expertos: se debe evaluar su competencia,
capacidad, objetividad e independencia dependiendo del riesgo
involucrado
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 6
RT 53
Informes
- Deben evitar vocablos o expresiones ambiguas que pudieran inducir a
error
- Deben ser escritos
- Contiene generalmente (además de lo requerido para cada tipo de
encargo):
• Titulo
• Destinatario
• Identificación de la información objeto del trabajo y motivo del encargo (de corresponder)
• Descripción breve de las responsabilidadesdel emisor de la información y del contador
• Indicación de la tarea realizada
• Opinión que ha podido formarse y conclusión a la que se ha llegado
• Elementos adicionales para una mejor comprensión
• Lugar y fecha de emisión
• Identificación y firma del profesional
• Clausulas de restricciones a su distribución
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 7
RT 53
Informes
- Dictamen
• Opinión no modificada o favorable sin salvedades
• Opiniones modificadas
• Opinión favorable con salvedades
• Opinión adversa
• Abstención de opinión
- Párrafos de énfasis: cuestión expuesta en los estados contables que es
de tal importancia que resulta fundamental su inclusión para que se
comprendan dichos estados
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 8
RT 53
Auditoría y entendimiento del marco de control interno y los
sistemas
El contador debe:
- Obtener un conocimiento apropiado de la estructura del ente, sus
operaciones, sistemas, su control interno, las normas legales que le
son aplicables y las condiciones económicas propias y las del ramo de
sus actividades. Este conocimiento tiene que permitir identificar, de
ser aplicable, el uso de organizaciones de servicios para llevar a cabo
total o parcialmente los procesos que tienen un impacto en la
información fuente de los estados contables.
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 9
RT 53
Auditoría y entendimiento del marco de control interno y los
sistemas
El contador debe:
Obtener un conocimiento del control interno relevante para la auditoría (el que sea
necesario obtener para valorar los riesgos de incorrección significativa en las afirmaciones
relativas a tipos de transacciones, saldos contables o información a exponer y para diseñar
los procedimientos posteriores de auditoría que respondan a tales riesgos). El
conocimiento del control interno incluye el modo en que el ente ha respondido a los riesgos
derivados de la tecnología de la información. Se debe considerar la obtención de
conocimiento del control interno en relación con las organizaciones de servicios relevantes
para la auditoría. La evaluación del control interno es conveniente que se desarrolle en la
primera etapa del trabajo porque sirve de base para perfeccionar la planificación en cuanto
a los siguientes atributos de los procedimientos de auditoría:
Naturaleza: Técnica del procedimiento a ejecutar
Extensión: Cantidad de veces que se ejecutará el procedimiento (Q muestra)
Oportunidad: En qué momento se realizará el procedimiento
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 10
RT 53
Auditoría y entendimiento del marco de control interno y los
sistemas
En relación al ambiente de TI, mínimamente es recomendable
analizar los siguientes temas:
- Estructura de TI de la compañía
- Políticas, normas y procedimientos existentes que den soporte al
marco de control interno
- Como la organización da respuesta a los riesgos de TI
- Realizar un entendimiento de los sistemas relevantes que soportan los
procesos / transacciones que impactan en la opinión del contador
- Qué controles están implementados para responder a esos riesgos
- Como están diseñados dichos controles y qué dependencias de TI
presentan
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 11
RT 53
Escepticismo Profesional
La aplicación del escepticismo profesional incluye:
Hacer una evaluación
Mantener una mente
crítica de la
abierta acerca de la
Estar alerta a las
suficiencia , validez y
honestidad y la
circunstancias inusuales
fiabilidad de las
integridad de la
que requieren más
pruebas de auditoría
administración y los
investigación o
obtenidas
encargados del
evidencia que
Directorio hasta que
contradiga o ponga en
se concluyan las
duda la fiabilidad de los
investigaciones.
documentos y las
respuestas obtenidas.
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 12
Normas ISA
¿Qué son las normas ISA?
• Son los International Standards on Auditing (ISA), en
español NIA (Normas Internacionales de Auditoría)
• Son emitidos por la International Auditing and Assurance
Standard Board (IAASB), órgano auspiciado por la
International Federation of Accountants (IFAC).
• La misión del IAASB es
Establecer, de manera
independiente y por su Facilitar la convergencia
propia autoridad, entre los estándares
estándares de alta calidad nacionales y los
que se refieran a la estándares
auditoría, revisión, otro internacionales.
aseguramiento, control de
calidad y servicios
relacionados.
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 13
Normas ISA
200-299 Principios y responsabilidades generales
ISA 200 Objetivos globales del auditor independiente y realización de la auditoría de
conformidad con las normas internacionalesde auditoría
ISA 210 Acuerdo de los términos del encargo de auditoría
ISA 220 Control de calidad de la auditoría de estadosfinancieros
ISA 230 Documentación de la auditoria
ISA 240 Responsabilidades del auditor en la auditoría de estados financieros respecto al
fraude
ISA 250 Consideración de las disposiciones legales y reglamentarias en la auditoría de
estadosfinancieros
ISA 260 Comunicación con los responsables del gobierno de la entidad
ISA 265 Comunicación de las deficiencias en el control interno a los responsables del
gobierno y a la dirección de la entidad
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 14
Normas ISA
300-499 Evaluación de riesgo y respuesta al riesgo evaluado
ISA 300 Planificación de la auditoria de estadosfinancieros
ISA 315 Identificación y evaluación de los riesgos de error material mediante el
conocimiento de la entidad y de su entorno
ISA 320 Importancia relativa o materialidad en la planificación y ejecución de la auditoría
ISA 330 Respuestas del auditor a los riesgos evaluados
ISA 402 Consideraciones de auditoría relativas a una entidad que utiliza una organización
de servicios
ISA 450 Evaluación de las errores identificados durante la realización de la auditoría
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 15
Normas ISA
500-599 Evidencia de auditoría
ISA 500 Evidencia de auditoria
ISA 501 Evidencia de auditoria – consideraciones específicaspara determinadasáreas
ISA 505 Confirmaciones externas
ISA 510 Encargos iniciales de auditoria – saldos de apertura
ISA 520 Procedimientos analíticos
ISA 530 Muestreo de auditoría
ISA 540 Auditoría de estimaciones contables, incluidas las de valor razonable, y de la
información relacionadaa revelar
ISA 550 Partes relacionadas
ISA 560 Hechos posteriores alcierre
ISA 570 Empresa en marcha
ISA 580 Manifestaciones escritas
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 16
Normas ISA
600-699 Uso del trabajo de otros
ISA 600 Consideraciones especiales – auditorías de estados financieros de grupos (incluido
el trabajo de los auditores de los componentes)
ISA 610 Uso del trabajo de los auditores internos
ISA 620 Uso del trabajo de expertos
700-799 Conclusiones de la auditoría y reporte
ISA 700 Formación de la opinión y emisión del informe de auditoría sobre los estados
financieros
ISA 701 Comunicación de los “key audit matters” en el informe del auditor independiente
ISA 705 Opinión modificada en el informe emitido por un auditor independiente
ISA 706 Párrafos de énfasis y párrafos sobre otras cuestiones en el informe emitido por un
auditor independiente
ISA 710 Información comparativa – cifras correspondientes de períodos anteriores y
estadosfinancieros comparativos
ISA 720 Responsabilidad del auditor con respecto a otra información incluida en los
documentos que contienen los estados financieros auditados
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 17
Normas ISA
800-899 Áreas especializadas
ISA 800 Consideraciones especiales – Auditorías de estados financieros preparados de
conformidad con un marco de información con fines específicos
ISA 805 Consideraciones especiales – Auditorías de un solo estado financiero o de un
elemento, cuenta, o partida específicos de unestado financiero
ISA 810 Encargos para informar sobre estadosfinancieros resumidos
• El IAASB también emite estándares relacionados a otro tipo de
encargos, por ejemplo los INTERNATIONAL STANDARDS
ON ASSURANCE ENGAGEMENTS (ISAE), entre los cuales se
destaca el ISAE3402 → Informes de Aseguramiento sobre
los controles en una Organización de Servicios
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 18
Normas ISA
Ejemplo de referencia a temas de sistemas
y procesos – ISA 315
63. La extensión y naturaleza de
los riesgos para el control interno
varían dependiendo de la
naturaleza y características del
sistema de información de la
entidad. Por lo tanto, para
entender el control interno, el
auditor considera si la entidad ha
respondido de manera adecuada a
los riesgos que se originan por el
uso de TI o sistemas manuales
estableciendo controles efectivos.
81. El auditor deberá obtener un
entendimiento del sistema de
información, incluyendo los
procesos de negocio
relacionados, relevante para la
información financiera
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 19
Estándares IIA
Asociación profesional mundial con más de 200.000 miembros en más de 170
países. El IIA establece los puntos de referencia para la forma en que se llevan a cabo
las actividades de auditoría interna en todo el mundo. En Argentina es el IAIA
(Instituto Argentino de Auditores Internos).
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 20
Estándares IIA
Nuevo estándar global de auditoría interna (IIA 2024)
• Es la versión actualizada del Marco Internacional para la Práctica
Profesional (MIPP), publicado en enero de 2024 por el IIA Global.
Reemplaza las Normas de 2017 y el anterior Código de Ética. Establece los
principios, requisitos y guías que deben seguir todos los auditores
internos profesionales.
• Se basa en 5 dominios:
• Propósito de la auditoría interna
• Ética y profesionalidad
• Gobierno de la función de auditoría interna
• Gestión de la función de auditoría interna
• Desempeño de los servicios de auditoría interna
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 21
Estándares IIA
Anterior (IPPF) Nuevo estándar 2024
Reemplazado por el Dominio II “Ética y
Código de Ética separado
profesionalidad”
3 tipos de normas: atributos, Reemplazados por 5 dominiosy 15
desempeño y guía principios
Guías obligatorias y recomendadas Unificado bajo el nuevo Marco Global
separadas (IPPF) Integrado
Se incluye sección específica con
Menor énfasis en evidencia
ejemplos de evidencia esperada
Mayor claridad con uso consistente de
Lenguaje a veces ambiguo
“debe”, “debería” y “podrá”
Se aclara cómo aplicarlo en
Aplicación menos flexible organizaciones pequeñas o sector
público
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 22
Estándares IIA
Objetivos y enfoque del nuevo estándar
• Aumentar la confianza de stakeholders en la auditoría interna.
• Promover ética, independencia y competencia profesional.
• Servir como base para la mejora continua de calidad.
• Reforzar el rol de la auditoría interna en el interés público.
¿Por qué es importante para las organizaciones?
• Refuerza la autoridad, el mandato y la calidad de la auditoría interna.
• Ayuda a integrar riesgos, controles y gobierno.
• Mejora la preparación ante evaluaciones externas de calidad.
• Estimula un enfoque sistemático y alineado con otras normas (COSO, ISO 31000, etc.).
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 23
Estándares IIA
Dominio I – Propósito de la Auditoría Interna
1.Demostrar integridad
2.Mantener la objetividad
3.Demostrar competencia
4.Ejercer el debido cuidado profesional
5.Mantener la confidencialidad
Dominio II – Ética y Profesionalidad
6.Autorización del Consejo (board)
7.Posicionarse de manera independiente
8.Supervisión del Consejo
Dominio III – Gobierno de la Función de Auditoría Interna
9.Planificar estratégicamente
10.Gestionar los recursos
11.Comunicar de manera eficaz
12.Mejorar la calidad
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 24
Estándares IIA
Dominio IV – Gestión de la Función de Auditoría Interna
13.Planificar eficazmente los trabajos
Dominio V – Desempeño de los Servicios de Auditoría Interna
14.Ejecutar los trabajos
15.Comunicar las conclusiones del trabajo y monitorear los planes de acción
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 25
Estándares IIA
Ejemplo: Principio 2 – Mantener la objetividad
Los auditores internos mantienen una actitud imparcial y libre de sesgo cuando
llevan a cabo los servicios de auditoría interna y toman decisiones.
Normas aplicables → Norma 2.1 – Objetividad individual
Requisito: Los auditores internos deben mantener su objetividad profesional
en la realización de todos los aspectos de los servicios de auditoría interna.
Consideraciones para la implementación: La objetividad implica que los
auditores realizan su trabajo sin comprometer su juicio profesional. Las
capacitaciones, metodologías y políticas internas ayudan a reforzar esta
actitud.
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 26
Estándares IIA
Ejemplos de evidencia de conformidad
• Registros de participación en formaciones sobre ética y objetividad.
• Declaraciones firmadas por los auditores internos que reconocen su
compromiso con el comportamiento ético.
• Documentación de las metodologías para tratar conflictos de interés o sesgos.
• Comunicaciones entre auditores y supervisores abordando posibles situaciones
que podrían comprometer la objetividad.
• Revisión documentada de los papeles de trabajo que evidencien independencia
de criterio.
• Inclusión de la objetividad como criterio en evaluaciones de desempeño.
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 27
GTAG
• El IIA también emite las GTAG – Global Technology Audit Guides, guías
prácticas para la realización de actividades de auditoría relacionadas con sistemas de
información y tecnología. En la actualidad son las siguientes:
https://www.theiia.org/en/standards/2024-standards/global-guidance/
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 28

### Relación con otros temas
*   **Conexión con el marco general:** Este tema se integra con los conceptos de seguridad y auditoría del curso.

---


## 17. Planificación, Ejecución y Pruebas de Auditoría

### Conceptos Principales

* Seguridad Informática y
* Principios de Auditoría
* Proceso de Auditoría
* Profesor Pablo Gil
* Agenda
* Proceso de Auditoría:
* • Definición de alcance, pruebas de control y sustantivas
* • Entendimiento, evaluación y validación del ambiente de control interno
* • Relevamiento y evaluación del diseño de controles
* • Pruebas de controles (efectividad operativa de los controles)

### Puntos Clave

* • Informes de auditoria
* UBA FCE –Seguridad Informática y Principios de Auditoría Slide2
* Seguridad Informática y
* Principios de Auditoría
* Definición de alcance, pruebas de control y
* sustantivas
* Profesor Pablo Gil
* Definición de alcance
* Auditoría Externa Auditoría Interna
* Materialidad Plan basado en análisis de

### Información Adicional

riesgos
Análisis cuantitativo y cualitativo Consideración de aspectos de
foco del directorio y de la alta
gerencia
Procedimientos de Seguimiento observaciones
impredecibilidad períodos anteriores
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 4
Enfoque del trabajo de auditoría interna
Plan de Pruebas de
Planificación
auditoría controles
Informes Seguimiento
Pruebas de controles
Basado en: requerimientos normativos, riesgo y aspectos operativos
importantes para la dirección
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 5
Enfoque del trabajo de auditoría externa
Evaluar si es necesario contar con
evidencia adicional
(procedimientos
analíticos o de detalle)
Evidencia
deseada aturaleza
Procedimientos análíticos
para validar
sustantivos o de detalle
un A
lcance
saldo/rubro
portunidad
Pruebas de controles
Más controles = menos riesgo = menor cantidad de procedimientos sustantivos
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 6
Procedimientos analíticos - Tipos
‘el análisis de ratios significativos y tendencias incluida la investigación resultante de
fluctuaciones y relaciones que no concuerdan con otra información relevante o se
desvían de los montos previstos’. (ISA 520)
Análisis de tendencias Evolución de ingresos, saldos de cuentas,
Análisis de ratios EJ: endeudamiento (P/PN), solvencia
(A/P), liquidez (AC/PC), tesorería (AC-
inv/PC)
Pruebas de razonabilidad Ej: PxQ ventas estimadas vs saldo contable
Análisis de regresión Similar razonabilidad pero con métodos
estadísticos y relación entre variables
Escaneo analítico Revisión de transacciones en búsqueda de
valores inusuales
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 7
Pruebas sustantivas de detalle
Comprenden la examinación de evidencia física /
documentación soporte de ítems que componen el saldo de
una cuenta patrimonial o de resultados
• Examinación física (ej inventario)
• Recálculo
• Reconciliación
• Confirmación (con terceras partes ej bancos, abogados, clients)
• Re-ejecución
• Pruebas de corte
Aplicación de muestros estadísticos para tamaños de muestra
En ciertos casos puede utilizarse software de auditoria para ejecutar las
pruebas
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 8
Seguridad Informática y
Principios de Auditoría
Entendimiento, evaluación y validación del
ambiente de control interno
Profesor Pablo Gil
A Focus on Control Activities
El “ciclo de confort” para la auditoría de control interno
Evaluación
Entendimiento
(diseño)
Validación (pruebas)
UBA FCE –Seguridad Informática y Principios de Auditoría
A Focus on Control Activities
Entendimiento: documentación del control interno
Características
¿Cómo se documenta?
En general, la documentación del control interno de un proceso (IT o negocio)
puede contar con los siguientes elementos:
→ Diagrama de flujo del proceso
→ Narrativa del proceso
→ Organigrama de las áreas que intervienen en el proceso
→ Matriz de riesgos y controles
→ Análisis de manuales y procedimientos del proceso
UBA FCE –Seguridad Informática y Principios de Auditoría
A Focus on Control Activities
Documentación del control interno
Al momento de documentar un control, deben considerarse los
siguientes aspectos
Realiza el control (tener en cuenta segregación de
QUIEN
funciones)?
Inputs son utilizados para realizar el control
(inputs/proceso/outputs)?
CUANDO Es que se realiza el control (tiempo/frecuencia)?
DONDE El control es ejecutado?
QUE Evidencia se deja luego de realizar el control?
COMO Se resuelven las excepciones?
UBA FCE –Seguridad Informática y Principios de Auditoría
A Focus on Control Activities
Documentación del control interno
Flujogramas
Objetivos El proceso debe ser documentado, con un nivel de detalle que permita
efectuar un seguimiento a las actividades y/o tareas que lo
comprenden, como también la fácil identificación de los controles que
posee.
Enfoque Debe ser lo suficientemente claro para comprender el proceso sin
necesidad de explicacionesadicionales.
Debe contener todos los documentos, archivos de datos, registros
contables, reportes importantes y sistemas que intervienen.
Para realizar el flujograma existen distintas herramientas (ej Visio)
UBA FCE –Seguridad Informática y Principios de Auditoría
Documentación del control interno
Narrativas
Objetivos Narrativa secuencial del proceso, indicando los controles existentes y
su referencia. La descripción secuencial, debe contener como mínimo
los siguientes pasos:
▪ Como se inicia el subproceso documentado
▪ Autorizaciones
▪ Registro de las transacciones
▪ Aplicación utilizada (Software)
▪ Cuál es fin de proceso y con que subproceso se conecta
Enfoque Se describen no tan solo los controles administrativos implementados,
sino que también se deben describir los “controles sistémicos
relevantes”.
UBA FCE –Seguridad Informática y Principios de Auditoría
A Focus on Control Activities
Documentación del control interno
Matriz de Controles
Control clave: Son los controles internos necesarios para prevenir o detectar errores u
omisiones importantes en relación a las aserciones financieras, para los procesos y
cuentas significativas.,
Objetivos Descripción precisa del control considerado “clave”, la cual además
señala los documentos, reportes e instancias de aprobación existentes,
documentadas y verificables por un tercero.
Responsables “Dueños” del proceso
Comentarios Deben señalarse los documentos/reportes que permitan
posteriormente, probar la existencia y funcionamiento del control por
parte de un tercero.
UBA FCE –Seguridad Informática y Principios de Auditoría
Documentación del control interno
Matriz de Controles
Formato Es una matriz generalmente realizada en Excel, que identifica para el proceso considerado
relevante, los siguientes contenidos:
1. Nombre del Proceso o subproceso.
2. Riesgo identificado.
3. Numero de referencia del control.
4. Descripción de todos los controles: Detalla si es una revisión analítica, conciliaciones,
procedimientos de revisión, acceso a sistemas, control de configuración de sistemas,
segregación de funciones
5. Aserciones de los estados financieros cubiertas por cada control (Existencia; Integridad;
Derechos y Obligaciones; Valuación y Presentación – exposición).
6. Tipo de control: Preventivo o Detectivo / Monitoreo o transaccional / Automatizado o
Manual.
7. Tipo de documento en que se materializa el control
8. Ejecutado por: Persona dueña del control.
9. Sistema / Aplicación que soporta el control.
UBA FCE –Seguridad Informática y Principios de Auditoría
Seguridad Informática y
Principios de Auditoría
Relevamiento y evaluación de diseño de
Controles
Profesor Pablo Gil
Relevamiento y evaluación de diseño de Controles
1. Entender el proceso de punta a punta
2.Identificar posibles riesgos
3. Identificar los puntos en el proceso donde el riesgo puede materializarse
4.Identificar controles y evaluar su diseño e implementación
5.Identificar y responder a deficiencias de control
Evaluación
Entendimiento
(diseño)
Validación (pruebas)
UBA FCE –Seguridad Informática y Principios de Auditoría
Relevamiento y evaluación de diseño de Controles
¿Qué es un walkthrough?
Técnica que implica realizar un seguimiento de una transacción desde
que se origina hasta que finaliza y se registra, si corresponde, en la
contabilidad. El procesamiento de la transacción es analizado a través
de los procesos y sistemas de la compañía.
UBA FCE –Seguridad Informática y Principios de Auditoría
Relevamiento y evaluación de diseño de Controles
¿Para que se utilizan los walkthrough?
• Realizar el entendimiento del proceso de punta a punta, evaluar el
diseño de los controles y determinar si los controles se encuentran
implementados (un caso).
• Entender el flujo de transacciones relacionadas a las aserciones /
objetivos de procesamiento de la información importantes o a las
operaciones relevantes que se deben cubrir de acuerdo al alcance
definido.
• Para la ejecución del WT, se utiliza una combinación de las técnicas de
indagación, observación, inspección y re-ejecución.
UBA FCE –Seguridad Informática y Principios de Auditoría 20
Relevamiento y evaluación de diseño de Controles
¿Para que se utilizan los walkthrough? (cont.)
• Se realizan preguntas con el objetivo de:
• Entender el proceso, incluyendo distintos tipos de transacciones
• Identificar puntos en el proceso donde no hay un control o el control
no esta diseñado de manera efectiva
• Indagar con la persona que realiza el control sobre cómo funciona el
control, la naturaleza de los errores detectados y el proceso de
corrección de dichos errores.
UBA FCE –Seguridad Informática y Principios de Auditoría
Relevamiento y evaluación de diseño de Controles
Otros aspectos a tener en cuenta
- Indagar/entrevistar a todas las personas que realizan los controles y
no solo al responsable del proceso.
- Corroborar lo relevado realizando procedimientos adicionales.
- Seguir una transacción desde su inicio a registración y utilizando la
misma documentación que retiene la persona que ejecuta el control.
- Realizar consultas relacionadas a como se mitigan y previenen los
riesgos de fraude.
UBA FCE –Seguridad Informática y Principios de Auditoría 22
Relevamiento y evaluación de diseño de Controles
Salidas
- Confirmar el entendimiento del proceso
- Identificar riesgos
- Evaluar el diseño y la implementación de los controles
- Sistemas que soportan el proceso
- Reportes & planillas de calculo relevantes
- Identificación de controles de acceso (Accesos críticos y SoD)
- Impacto contable de la transacción analizada
UBA FCE –Seguridad Informática y Principios de Auditoría 23
Seguridad Informática y
Principios de Auditoría
Pruebas de controles
Informes
Profesor Pablo Gil
Prueba/validación de controles
Evaluación
Entendimiento
(diseño)
Validación (pruebas)
Luego de evaluar si el diseño de los controles es adecuado, es necesario validar que
los controles han operado de forma efectiva durante todo el período sujeto a
revisión.
Es por ello que se realizan pruebas de controles. Normalmente los controles que
se prueban son los controles claves, que son aquellos que de manera más eficiente
generan “aseguramiento” sobre los objetivos de procesamiento de la información o
aserciones que el auditor busca validar.
UBA FCE –Seguridad Informática y Principios de Auditoría
Validación de controles
Evaluación
Entendimiento
(diseño)
Validación (pruebas)
6.Determinar estrategia de confianza en controles y evaluar riesgo (aud
externa)
7.Planificar pruebas de operatividad de controles
8.Probar controles y evaluar su efectividad
9. Identificar y responder a deficiencias de control
UBA FCE –Seguridad Informática y Principios de Auditoría
Validación de controles
¿Cuáles son los pasos a seguir a la hora de realizar
pruebas de controles?
1- Desarrollar el plan de pruebas
- En base al riesgo asociado al control y al objetivo que el control
intenta cubrir, seleccionar la naturaleza, oportunidad y alcance
adecuados para probar el control
2- Ejecutar la prueba
- Llevar a cabo el plan de pruebas diseñado. En algunos casos
será necesario obtener previamente el universo total de
transacciones asociadas al control para poder seleccionar una
muestra en base a los criterios definidos.
UBA FCE –Seguridad Informática y Principios de Auditoría
Validación de controles
¿Cuáles son los pasos a seguir a la hora de realizar
pruebas de controles?
3- Concluir sobre la efectividad del control
- Existe evidencia suficiente?
- El control operó durante todo el período?
- El control operó de la manera en que estaba diseñado?
4- Documentar los procedimientos realizados y las
conclusiones
UBA FCE –Seguridad Informática y Principios de Auditoría
Validación de controles
Tamaño de muestras
N casos dependiendo de la frecuencia
de operación del control (ej diaria,
Controles
semanal, mensual, etc). También se
manuales o
deben validar las fuentes de
dependientes de
información utilizadas para ejecutar
el control
Normalmente, si operan
Controles correctamente 1 vez, deberían operar
automáticos siempre de la misma manera
(siempre y cuando los CGTI que lo
soporten operen de manera adecuada
y no cambien los programas
asociados al control automático)
UBA FCE –Seguridad Informática y Principios de Auditoría
Validación de controles
Accesos
Comprender 1. Entender cómo se restringe el acceso a funciones del
configuración sistema
Identificar quién 2. Analizar quién posee acceso a las funciones que se
posee acceso están evaluando
Evaluar si el acceso 3. Obtener evidencia sobre si el acceso analizado
es adecuado corresponde a las personas adecuadas
UBA FCE –Seguridad Informática y Principios de Auditoría
Validación de controles
Segregación de funciones
Comprender el proceso
1. Entender de qué forma opera el proceso evaluado
de negocio
Identificar conflictos 2. Basado en el entendimiento anterior, identificar cuáles
potenciales son los casos potenciales de segregación de funciones
3. Para los conflictos identificados, comprender si la
Identificar controles gerencia los mitiga a través de restricción de accesos en
los sistemas o a través de controles compensatorios
4. Realizar las pruebas de los aspectos identificados en el
Probar los controles
punto 3)
UBA FCE –Seguridad Informática y Principios de Auditoría
Validación de controles
Interfaces
Pueden ser probadas de dos maneras:
A) Probar el funcionamiento automático de la interfase
• Validar la lógica de construcción entre la fuente y el destino
• Validar la operación de la transferencia de datos (la interfase se ejecutó? Si
hubo errores, cómo se corrigieron?)
B) Probar la reconciliación de datos como evidencia de la
operación de la interfase (control de negocio)
UBA FCE –Seguridad Informática y Principios de Auditoría
Informes
• Resumen ejecutivo
• Objetivo
• Alcance
• Hallazgos y oportunidades de mejora
- Descripción de la situación observada
- Descripción del efecto / riesgo
- Recomendación
- Comentarios de la gerencia
UBA FCE –Seguridad Informática y Principios de Auditoría

### Relación con otros temas
*   **Conexión con el marco general:** Este tema se integra con los conceptos de seguridad y auditoría del curso.

---


## 18. Seguridad en el SDLC y DevSecOps

### Conceptos Principales

* Seguridad Informática y
* Principios de Auditoria
* SSDLC - Seguridad en el Ciclo de Vida del
* Desarrollo del Software
* Profesor Pablo Gil
* Agenda
* ● Qué es el SDLC?
* ● Qué rol juega la seguridad en el SDLC?
* ● Metodologías
* ○ Tradicional Vs DevSecOps

### Puntos Clave

* ● Ciberataques más comunes
* ○ Ransomware
* ○ Phishing
* ○ Data breach / fuga de información
* ○ Ingenieria Social
* ○ Denegación de Servicio
* UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 2
* UBA FCE - Seguridad Informática y Principios de Auditoria
* Seguridad en SDLC
* UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 3

### Información Adicional

UBA FCE - Seguridad Informática y Principios de Auditoria
Seguridad en SDLC
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 4
UBA FCE - Seguridad Informática y Principios de Auditoria
Seguridad en SDLC
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 5
UBA FCE - Seguridad Informática y Principios de Auditoria
Fase de Diseño - Seguridad en SDLC
PRINCIPIOS DEL DISEÑO SEGURO
La seguridad empieza a incorporarse en el Software en el momento del diseño. El diseño seguro aparece
cuando se tiene en cuenta los aspectos anteriores y se aplican los siguientes principios:
Seguridad suficientemente buena Diseño abierto
Mínimo privilegio Mínimo mecanismo común
Separación de funciones Aceptación psicológica
Defensa en profundidad Hay que buscar un El eslabón más débil
equilibrio entre
Fallo seguro ambos principios Reutilización de componentes existentes
Economía de mecanismos Punto único de fallo
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 6
UBA FCE - Seguridad Informática y Principios de Auditoria
Fase de Implementación- Seguridad en SDLC
LA SEGURIDAD Y EL CONTROL
✔ Control de errores ante salidas técnicas ✔ Comentarios en inglés.
✔ Semántica re-utilizable. ✔ Manejo de LOGS de errores técnicos, servicios.
✔ Incorporación de métodos criptográficos. ✔ Utilizar herramientas de monitoreo de caída y entrada de
✔ Manejo de uso de memoria. usuarios.
✔ Manejo de uso de red. ✔ Establecer estándar de desarrollo con los distintos pares
✔ Control de archivos. de trabajo.
✔ Elaboración de reglas de negocio para reutilización. ✔ Limpiar metadata de archivos.
✔ Transferencia de conocimiento a pares técnicos tras ✔ No comentar endpoints.
documentación técnica.
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 7
UBA FCE - Seguridad Informática y Principios de Auditoria
Fase de Tesitng- Seguridad en SDLC
ACTIVIDADES DE SEGURIDAD
TESTING & DEPLOY
R1 INSPECCION DE CODIGO ( SEGURIDAD Herramientas de inspección de Código:
ESTÁTICA) • SONARQube
• Lint
• Fortify
R2 QA AUTOMATION Herramientas de automatización de pruebas
• Selenium
• Protractor
• Appium
• HP UFT
R3 CONFIG TEST DEPLOY Prueba de configuración de entornos de ambiente
• Balanceo de Maquinas
• Configuración de Puertos
• Configuración de la VIP
R4 REVISION METADATA Revisión de comentarios con posibles endpoints, imágenes con metadata
de direcciones
R5 QA TECNICO Pruebas de Stress, Envejecimiento, Balanceo a Servicios
R6 SEGURIDAD DINAMICA Top Ten OWASP
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 8
UBA FCE - Seguridad Informática y Principios de Auditoria
Fase de Implementación- Seguridad en SDLC
ACTIVIDADES DE SEGURIDAD
• Mantenimiento de las actividades anteriores:
(cid:0) Detección de requerimientos de seguridad en las historias técnicas.
(cid:0) Mantenimiento de la superficie de ataques durante el desarrollo.
(cid:0) Supervisión de la arquitectura (contra requisitos o estándares de referencia) durante el
desarrollo.
• Revisión de código – SAST (Static Application Security Testing, por sus siglas en ingles):
(cid:0) Automático (herramienta)
(cid:0) Manual (auditor de seguridad)
(cid:0) Manual (peer review-developer o Code Review)
• Pruebas unitarias de seguridad
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 9
UBA FCE - Seguridad Informática y Principios de Auditoria
Seguridad en SDLC
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 10
UBA FCE - Seguridad Informática y Principios de Auditoria
Fase de Operación - Seguridad en SDLC
ACTIVIDADES DE SEGURIDAD
• Proceso de Configuration Management:
(cid:0) Dependencias de otros artefactos
(cid:0) Archivos de configuración de los artefactos.
(cid:0) La documentación generada
• Patch Management y proceso de Bug Tracking.
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 11
UBA FCE - Seguridad Informática y Principios de Auditoria
Fase de Operación - Seguridad en SDLC
ACTIVIDADES DE SEGURIDAD
En esta imagen, se puede observar un ejemplo del uso de bug tracking:
Ejemplo de uso de Bug bar Impacto del fallo
No corregir, Corregir en la Corregir en la Desplegar
asumir el riesgo próxima versión / próxima release parche en
release menor (1.X) producción
mayor (X.0)
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 12
UBA FCE - Seguridad Informática y Principios de Auditoria
Seguridad en SDLC
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 13
UBA FCE - Seguridad Informática y Principios de Auditoria
Metodologias - Tradicional vs DevSecOps
From waterfall to Agile DevSecOps
Waterfall
Idea Design Plan Develop Verify Deploy Securize
Agile
Idea DE PL DEV VE DE PL DEV VE Deploy Securize
Agile + DevOps
Idea DE PL DEV VE DE DE PL DEV VE DE Securize
Agile + DevSecOps
Idea DE PL DEV VE SE DE DE PL DEV VE DE
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 14
UBA FCE - Seguridad Informática y Principios de Auditoria
Concepto de DevSecOps
DEV OPS SEC
Reducción de
Tiempo a market Visibilidad
Riesgos
Usabilidad Performance
Cumplimiento
Educación Protección
Escalabilidad
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 15
UBA FCE - Seguridad Informática y Principios de Auditoria
Concepto de DevSecOps
DEVS DEVS
COLABORATION PROCESS COMPLIANCE PROCESS
DEVOPS DEVSECOPS
IT IT QA
AUTOMATE AUTOMATE
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 16
UBA FCE - Seguridad Informática y Principios de Auditoria
DevSecOps
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 17
UBA FCE - Seguridad Informática y Principios de Auditoria
DevSecOps
Cybersecurity Design
Cybersecurity Workshops
Participates in product design by
The pod is educated and made
setting standards for rugged
aware
software
DevSecOps control
DevSecOps
Controls that the software automates
behaves according to the 
specified regulations Implement the cybersecurity
tests by including them in the
automations
Reliable development cycle
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 18
UBA FCE - Seguridad Informática y Principios de Auditoria
DevSecOps
AUTOMATION SEC ARCHITECT
EXPERT
Execute secure
Responsible for continuous architectural design
integration and delivery
tools. Automates builds and
tests.
AUDITS & ALERTS
APPs SECURITY Implements audit collection
subsystems of the application
It drives Rugged Software and measures the state of the
and automates security environment to generate
tests alerts
PROJECT SECURITY
SECURITY INDICATORS
It establishes the segregation
of functions and the access of
It establishes subsystems
developers and stakeholders
that allow to measure the
based on the regulations
security environment of the
environments.
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 19
UBA FCE - Seguridad Informática y Principios de Auditoria
DevSecOps
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 20
UBA FCE - Seguridad Informática y Principios de Auditoria
DevSecOps
Dinamico (DAST)
• Aplicación de herramientas (en diversos grados)
• Aplicación de ejercicios a través de UI / API
• Cero? Falsos positivos
Estatico (SAST)
• Dificil de implementar?
• Revision de codigo fuente
• Altos falsos negativos
• Análisis y control del flujo de • El reporte es un exploit Fuzzing
datos • A veces es difícil encontrar el código para • Instrumentacion (en diversos grados)
• Análisis de falsos positivos solucionarlo • Envios de entrada inesperadas por las API
• Mirar la respuesta y la salida de la instrumentación
• Rápido feedback a los
• Excelente para probar protocols como SIP
desarrolladores • Bueno para API REST
• Tiempos de ejecución que pueden ser altos
• Es difícil encontrar el código para solucionar el
problema
Analisis de composicion de
software (SCA)
Seguridad de la aplicación en
• Identifique dependencias y
tiempo de ejecución
versiones
• Verifique CVE / NVD +... para Proteccion (RASP)
conocer las vulnerabilidades
Reportes de "mal" comportamiento
reportadas
• Propuesta de versión/parche Se puede abortar la transacción o matar
para remediación
• Verificar licencias vs políticas el proceso para protegerla
• Moverse agil
• Algunos tienen modo firewall
IAST
• Combinación de estatico y dinamico
• Buenos falsos positivos y negativos
• Aún inmaduro, pero próximamente
seguro
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 21
UBA FCE - Seguridad Informática y Principios de Auditoria
¿Por qué asegurar el SDLC?
CLASES DE AMENAZAS (STRIDE) ASPECTO DE SEGURIDAD
Spoofing (Suplantación de identidad) Autenticación
Tampering (Modificación de datos) Integridad Relacionados con el
usuario
Repudiation (Repudio) No Repudio
Information Disclosure (escape de información) Confidencialidad Relacionados con la
información
Denial of Service (denegación de servicio) Disponibilidad
Elevation of Privileges (escalado de privilegios) Autorización
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 22
UBA FCE - Seguridad Informática y Principios de Auditoria
Ataques más comunes
(Manual de Ciberseguridad en español)
Ransomware:
El Ransomware es un Malware (software malicioso) que bloquea nuestro dispositivo, pudiendo llegar a cifrar el
contenido del disco duro. Una vez que hemos perdido el control sobre nuestro equipo nos reclaman el pago de un
rescate que habitualmente se solicita en criptomonedas, bitcoins por ejemplo.
Phishing:
El phishing es una técnica utilizada por ciberdelincuentes para, haciéndose pasar por una entidad o persona de
confianza, a través del correo electrónico u otros canales de comunicación, robarnos información confidencial como
nombres de usuario, contraseñas y datos de tarjetas de crédito, entre otras, mientras accedemos a un servicio web que
creemos seguro y legítimo
Data breach / fuga de información
Todos los incidentes de fuga de información nos constatan lo difícil que es proteger la confidencialidad de la
información, por otro lado, el activo más valioso de cualquier organización. Denominamos incidentes de fuga de
información a aquellos incidentes que ponen en poder de una persona ajena a la organización, información
confidencial.
Ingenieria Social:
Consiste en conseguir engañar a alguien con el objetivo de conseguir de ellos lo que se desee . A partir de este ataque, a
un trabajador, como se inician ataques como los descritos anteriormente: malware, ransomware.
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 23
UBA FCE - Seguridad Informática y Principios de Auditoria
Por si nos queda tiempo...
Black Box White Box Grey Box
The Ethical Hacker doesn't have any The Ethical Hacker has complete open The Ethical Hacker has partial information
information about the structure of the access to application and system. This about the application internals. For example
application, its components and internals allows him to view source code and be Platform vendor, sessionID generation
granted high-level privilege accounts to the algorithm, diagram, test users,etc.
network.
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 24
UBA FCE - Seguridad Informática y Principios de Auditoria

### Relación con otros temas
*   **Conexión con el marco general:** Este tema se integra con los conceptos de seguridad y auditoría del curso.

---


## 19. Tipo de amenazas y ataques. Técnicas de explotación

### Conceptos Principales

* Auditoría y Seguridad de los
* Sistemas de Información
* “Tipo de amenazas y ataques. Técnicas de
* explotación”
* Profesor Pablo Gil
* Temario
* ➢ ¿Que es una amenaza?
* ➢ MITRE ATT&CK
* ➢ Método STRIDE
* ➢ Definiciones importantes

### Puntos Clave

* ○ Superficie de ataque
* ○ IoC / IoA
* ➢ Malware
* ➢ Tipos de Malware:
* ○ Ransomware
* ○ Spyware
* ○ Adware
* ○ Worm
* ○ Troyano
* ○ Botnets

### Información Adicional

➢ Ejemplos de Ciberataques
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 2
Que es una amenaza?
Una amenaza es un circunstancia, un evento o una
persona con intención de causar daño a un sistema en
las siguientes formas:
Las 4 D`s que la determinan
✔ Destruction (Destrucción)
✔ Disclosure (Divulgación)
✔ Data modification (Mod. de Datos)
✔ Denial of Service (Denegación de Servicio)
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 3
MITRE ATT&CK
El marco MITRE ATT&CK (Adversarial Tactics,
Techniques, and Common Knowledge) es una
h erramienta dinámica que utilizan las
organizaciones para comprender y mitigar las
amenazas de ciberseguridad.
1) Matriz ATT&CK
2) Navigator ATT&CK.
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 4
MATRIZ ATT&CK
La Matriz ATT&CK es una lista de tácticas y
técnicas que los atacantes usan para
c omprometer la seguridad de una organización,
organizadas en objetivos de alto nivel (tácticas) y
los métodos específicos (técnicas) para
alcanzarlos.
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 5
Método STRIDE
STRIDE (Spoofing, Tampering, Repudiation, Information
disclosure, Denial of service, and Elevation of privilege)
Desarrollado por Microsoft, STRIDE es una metodología de modelización de amenazas para
identificar problemas de seguridad en aplicaciones de software. Se enfoca en seis categorías de
amenazas:
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 6
Definiciones importantes
Superficie de ataque: es la suma de vulnerabilidades, rutas
o métodos, a veces llamados vectores de ataque, que los
hackers pueden usar para obtener acceso no autorizado a la
red o datos confidenciales, o para llevar a cabo un
ciberataque. Es decir, todos los puntos de entrada y
vulnerabilidades potenciales que un atacante puede utilizar
para explotar o vulnerar un sistema, red o aplicación.
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 7
Definiciones importantes
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 8
Definiciones importantes - Ejemplos
https://www.linkedin.com/posts/rafa
el-huam%C3%A1n-medina_ciberse
guridad-ciberdefensa-seguridaddel
ainformacion-activity-72056157290
33785344-SjG8?utm_source=share
&utm_medium=member_desktop
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 9
Algunas Datos
La región de América Latina y el Caribe sufrió un total de
200.000 millones de intentos de ataques cibernéticos
durante 2023
En Argentina fueron 2.000 millones. En comparación con
2022 (10.000 millones) hubo una fuerte reduccion, pero na
mayor sofisticacion y direccionamiento
https://www.forbesargentina.com/innovacion/argentina-sufrio-2000-millones-intentos-ciberataques-2023-informe-n50386
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 10
Tipos de Amenazas - Malware
El malware es un software o código informático diseñado
para infectar, dañar o acceder a sistemas informáticos.
Malware es un término general para referirse a cualquier tipo
de «malicious software»
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 11
Tipos de Amenazas - Tipos Malware
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 12
Tipos Malware - Ransomware
Funciona bloqueando o denegando el acceso a su
dispositivo y sus archivos hasta que se pague un rescate.
El ransomware puede propagarse de varias formas:
● Enlaces
● Archivos adjuntos maliciosos de correo electrónico
● Mensajes de phishing
● Vulnerabilidades de día cero (Zero day)
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 13
Tipos Malware - Spyware
Recaba información sobre un dispositivo o red para luego
enviársela al atacante.
Spyware como Pegasus suelen ser utilizados para
supervisar la actividad en Internet de una persona y recopilar
datos personales, incluidas credenciales de inicio de sesión,
números de tarjeta de crédito o información financiera que
se puede usar para cometer robo de identidad.
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 14
Tipos Malware - Adware
Se usa para generar ingresos para el desarrollador de
malware, la idea es que bombardea el dispositivo infectado
con anuncios no deseados.
Tipos comunes:
● Juegos gratuitos
● Barras de herramientas para el navegador
Esta clase de adware recaba datos personales acerca de la
víctima y después los emplea para personalizar los anuncios
que muestra.
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 15
Tipos Malware - Gusanos o worm
Están diseñados para proliferar.
1. Infecta un equipo
2. Se replica
3. Se extiende a dispositivos adicionales
4. Permanece activo en todas las máquinas afectadas
Algunos gusanos actúan como mensajeros para instalar
malware adicional. Otros tipos están diseñados solo para
extenderse y no causan daño intencionadamente a las
máquinas anfitrionas, aunque siguen atestando las redes
con sus demandas de ancho de banda.
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 16
Tipos Malware - Troyanos
Son un tipo de malware utilizado para ocultar otro. El
malware troyano se infiltra en el dispositivo de una víctima
presentándose como software legítimo. Una vez instalado, el
troyano se activa y, en ocasiones, llega incluso a descargar
malware adicional.
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 17
Tipos Malware - Redes de Robots o Botnets
Es una red de equipos o de código informático que
desarrolla o ejecuta malware. Los atacantes infectan un
grupo de equipos con software malicioso conocido como
«r obots» (o «bots»), capaz de recibir órdenes desde su
controlador.
Los equipos conectados en una botnet forman una red que
proporciona al controlador acceso a una capacidad de
procesamiento sustancial. Dicha capacidad puede
emplearse para coordinar ataques DDoS, enviar spam,
robar datos y crear anuncios falsos en su navegador.
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 18
Ejemplos de Ciberataque - SolarWinds
Contexto y Descubrimiento
En diciembre de 2020, se descubrió que SolarWinds, una empresa de software de gestión de
TI, había sido comprometida por un sofisticado ataque cibernético. El ataque fue detectado
inicialmente por la empresa de seguridad FireEye, que también fue víctima del ataque.
Vector de Ataque
El ataque involucró la inserción de un código malicioso en las actualizaciones de software de
SolarWinds, específicamente en su plataforma Orion. Este software es utilizado por
numerosas empresas y organizaciones gubernamentales para gestionar sus redes y
sistemas.
Metodología del Ataque
Supply Chain Attack: Los atacantes comprometieron el proceso de desarrollo de software de
SolarWinds, insertando un malware denominado "Sunburst" en las actualizaciones legítimas
de Orion. Este malware se distribuía junto con las actualizaciones a los clientes de
SolarWinds, convirtiéndose en una amenaza generalizada.
Backdoor: Una vez instalado, Sunburst creaba una puerta trasera que permitía a los
atacantes acceder y controlar los sistemas afectados sin ser detectados.
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 19
https://www.clarin.com/tecnologia/uba-sufrio-ciberataque-docentes-alumnos-pueden-acceder-sistemas_0_hSLyvy1RGy.html
Ejemplos de Ciberataque - SolarWinds
Alcance y Impacto
Se estima que alrededor de 18,000 clientes de SolarWinds descargaron las actualizaciones
infectadas. Entre los afectados se encontraban varias agencias gubernamentales de Estados
Unidos.
El ataque pasó desapercibido durante varios meses, lo que permitió a los atacantes obtener
acceso prolongado a las redes comprometidas y exfiltrar datos sensibles.
¿Por que fue relevante? → Por el tipo de clientes que tiene la empresa
¿A quien se le atribuye el ataque? → Hubo declaraciones que acusaban a los rusos
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 20
Ejemplos de Ciberataque - telecom
Contexto y Descubrimiento
En julio de 2020, Telecom Argentina, una de las mayores empresas de telecomunicaciones
del país, sufrió un ciberataque significativo que comprometió sus sistemas y datos. El ataque
fue detectado cuando se observaron problemas en los sistemas internos de la empresa, lo
que llevó a la identificación de un ataque de ransomware.
Metodología del Ataque
- Ransomware: El malware utilizado en este ataque fue el ransomware "REvil" (también
conocido como Sodinokibi). Este ransomware es conocido por encriptar archivos y
exigir un rescate a cambio de la clave de desencriptación.
- Acceso Inicial: Aunque los detalles específicos sobre cómo se obtuvo el acceso inicial
no se hicieron públicos, es común que estos ataques comiencen con correos de
phishing, explotación de vulnerabilidades no parcheadas o credenciales
comprometidas.
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 21
https://www.clarin.com/tecnologia/uba-sufrio-ciberataque-docentes-alumnos-pueden-acceder-sistemas_0_hSLyvy1RGy.html
Ejemplos de Ciberataque - SolarWinds
Alcance y Impacto
Encriptación de Datos: El ransomware encriptó una gran cantidad de archivos críticos
en los sistemas internos de Telecom, lo que afectó la capacidad de la empresa para
operar normalmente.
Demanda de Rescate: Los atacantes exigieron un rescate de 7.5 millones de dólares
en criptomonedas para proporcionar las claves de desencriptación. Esta cifra fue una
de las más altas demandadas en ese momento por un ataque de ransomware.
Servicios Interrumpidos: Si bien el ataque afectó los sistemas internos y la capacidad
operativa de la empresa, no se informó de interrupciones significativas en los servicios
hacia los clientes, lo cual fue un aspecto positivo en la respuesta de Telecom.
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 22
Bibliografía
➢ https://www.globalsuitesolutions.com/es/que-es-marco-mitre-att-ck/
➢ Virus 101 - Symantec PPT
➢ https://www.linkedin.com/pulse/un-acercamiento-al-modelado-de-amenazas-base4-sec
urity-03aff/
➢ https://www.avast.com/es-es/c-malware
➢ https://www.avast.com/es-es/c-what-is-ransomware
➢ https://www.welivesecurity.com/la-es/2022/03/29/que-son-indicadores-ataque/
➢ https://support.kaspersky.com/KESWin/11.7.0/es-MX/213408.htm
➢ https://www.solarwinds.com/es
➢ https://www.xataka.com/pro/ataque-a-solarwinds-explicado-que-ataque-a-esta-empresa
-desconocida-trae-cabeza-a-grandes-corporaciones-gobiernos-mundo
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 23

### Relación con otros temas
*   **Conexión con el marco general:** Este tema se integra con los conceptos de seguridad y auditoría del curso.

---


## 20. Detección y respuesta a incidentes de Seguridad

### Conceptos Principales

* Seguridad Informática y
* Principios de Auditoría
* Detección y respuesta a
* incidentes de Seguridad
* Profesor Pablo Gil
* ¿Que es un incidente de seguridad?
* Un incidente o evento de seguridad es cualquier vulneración digital
* o física que pone en peligro la confidencialidad, integridad o
* disponibilidad de los sistemas de información o datos sensibles de
* una organización.

### Puntos Clave

* ● Ransomware
* ● Phishing e ingeniería social
* ● Ataques DDoS
* ● Ataques a la cadena de suministro
* ● Amenazas internas
* ● Ataques de escalada de privilegios
* ● Ataques de intermediario
* UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 2
* Eventos (Log)
* • Cualquier ocurrencia que se registra dentro de un sistema o red.

### Información Adicional

Esto puede incluir acciones rutinarias como el inicio de sesión de un usuario, el acceso a
un archivo, o los cambios de configuración del sistema.
• NO necesariamente indican un problema de seguridad. Son
simplemente actividades que se monitorean para tener un registro
de lo que está sucediendo en el sistema.
• La mayoría de los eventos son benignos y no requieren
intervención. Sin embargo, algunos eventos pueden ser parte de un
patrón que, si se observa en conjunto, puede señalar un problema.
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 3
Eventos - ejemplo
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 4
Principales Herramientas de Monitoreo
• SIEM (Security Information and Event Management / Gestión de Eventos e
Información de Seguridad)
• WAF (Web Application Firewall) / Firewall
• IDS (Intrusion Detection System / Sistema de detección de intrusiones)
• IPS (Intrusion Prevention System / sistema de prevención de intrusiones)
• EDR / XDR (Detección avanzada de amenazas y respuesta automatizada)
Las herramientas de monitoreo son esenciales en la detección de
incidentes. Ya que en la correlación de eventos se pueden encontrar
comportamientos anómalos que son un potencial incidente
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 5
Incidentes - ejemplos
• Acceso no autorizado
• Ataque de malware
• Intento de exfiltración de datos
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 6
Proceso de manejo de incidentes
Un proceso de manejo de incidentes está diseñado para permitir que
una organización restablezca el servicio cuando un servicio está
inactivo o degradado.
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 7
Ciclo de vida de un Incidente
1. Detección
2. Análisis
3. Contención
4. Erradicación
5. Recuperación
6. Lecciones aprendidas
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 8
Ciclo de vida de un Incidente
1. Detección: ¿cómo se va a detectar el incidente?
2. Análisis: ¿cómo se va a determinar el alcance del impacto?
3. Contención: ¿cómo se va a aislar el incidente para limitar el
alcance?
4. Erradicación: ¿cómo se va a eliminar la amenaza del entorno?
5. Recuperación: ¿cómo se va a conseguir que el sistema o recurso
afectado vuelva a ser productivo?
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 9
Respuesta a incidentes
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 10
Respuesta a incidentes - Buenas prácticas
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 11
Respuesta a incidentes - Resumen
• Esta alerta es un problema real. ¿Y ahora qué?
• Minimizar y limitar el alcance del incidente
• Proteger la experiencia del usuario externo lo más rápido posible
• Entender que la contención es siempre mejor que la resolución
• Marco para tomar decisiones bajo presión
• Utilizar las herramientas de contención correctas
• Para una mitigación rápida, el factor más importante es tomar
buenas decisiones.
• Detectar a tiempo, cada minuto cuenta
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 12
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 13

### Relación con otros temas
*   **Conexión con el marco general:** Este tema se integra con los conceptos de seguridad y auditoría del curso.

---


## 21. Pruebas de Penetración & Auditoría de código

### Conceptos Principales

* Seguridad Informática y
* Principios de Auditoría
* Pruebas de Penetración &
* Auditoría de código
* Profesor Pablo Gil
* Objetivos de la clase
* • Explicar qué son las pruebas de penetración y la
* auditoría de código
* • Describir la importancia de ambas en la seguridad
* de los sistemas

### Puntos Clave

* • Presentar las habilidades y herramientas
* necesarias para realizar estas actividades
* UBA FCE –Auditoría y Seguridad de los Sistemas de Información Slide 2
* Pruebas de penetración
* Metodología de prueba en la que los evaluadores, que suelen trabajar
* con limitaciones específicas, intentar eludir o sortear las
* caracteristicas de seguridad de un sistema. El objetivo es
* determinar si las vulnerabilidades internas o entre componentes
* pueden ser explotadas para comprometer el sistema, sus datos o
* los recursos de su entorno.

### Información Adicional

https://csrc.nist.gov/glossary/term/penetration_testing
UBA FCE –Auditoría y Seguridad de los Sistemas de Información Slide 3
Auditoría de código
Una revisión de código, o auditoría, investiga las prácticas de
codificación utilizadas en la aplicación. El objetivo principal de
estas revisiones es descubrir defectos de seguridad y
potencialmente identificar soluciones.
https://csrc.nist.gov/glossary/term/security_oriented_code_review
UBA FCE –Auditoría y Seguridad de los Sistemas de Información Slide 4
Pruebas de penetración & auditoría de código
Identificar y mitigar
vulnerabilidades de
seguridad Uso de herramientas
Simulación de ataques
externos vs. revisión de
código internamente
Etapas de planificación, Desarrollo seguro
ejecución, análisis de
resultados, generación de
informes de vulnerabilidades
encontradas y recomendaciones
para mitigarlas
UBA FCE –Auditoría y Seguridad de los Sistemas de Información Slide 5
Pruebas de penetración
UBA FCE –Auditoría y Seguridad de los Sistemas de Información Slide 6
Definicion de Hacker vs. Cracker
UBA FCE –Auditoría y Seguridad de los Sistemas de Información Slide 7
Pruebas de penetración
UBA FCE –Auditoría y Seguridad de los Sistemas de Información Slide 8
Pruebas de penetración - etapas
UBA FCE –Auditoría y Seguridad de los Sistemas de Información Slide 9
Acceso a las redes
Ingeniería Social
Interna Externa
Se pueden realizar diferentes Se pueden combinar también
tipos de pruebas, cada una está conCampañadephishingpara
diseñadaparaencontrartodoslos probarlamadurezdela
posiblesproblemasdeseguridad. usuariofinal
Explotación de Vulnerabilidad Reporte manual o automático
Al finalizar se envía el reporte con
Cuando terminemos la parte de detección todo el listado de las vulnerabilidades
parte, avanzamos con la encontradas
explotación de las vulnerabilidades
críticas encontradas
UBA FCE –Auditoría y Seguridad de los Sistemas de Información Slide 10
Tipos de métodos para ejecutar la prueba de
penetración
Caja negra Caja Blanca Caja Gris
El hacker no tiene información El Hacker tiene completo El hacker tiene información
sobre la infraestructura, la acceso a los sistemas y las parcial sobre las aplicaciones
aplicación, las configuraciones aplicaciones. Esto le permite internas. Por ejemplo,
de red, etc. revisar el código fuente y tiene conocimiento de plataformas
privilegios sobre las utilizadas, ID de sesión para
configuraciones de red generación de algoritmos,
diagramas, usuario de prueba,
etc.
UBA FCE –Auditoría y Seguridad de los Sistemas de Información Slide 11
Fases de un prueba de penetración
UBA FCE –Auditoría y Seguridad de los Sistemas de Información Slide 12
Relevamiento Tecnico
DESCUBRIMIENTO PLAN DE
ACCIÓN
ACTIVIDADES RESULTADO ACTIVIDADES RESULTADOS
● Reuniones con el CISO y los ESPERADO ● Definición de equipo, ESPERADOS
REPORTE
interesados tiempos y tareas con el ● Programacon
● Diagnóstico
acuerdo de los directores iniciativas
● Análisis de la cultura de sobre la
de la compañía que priorizadas
Ciberseguridad de la madurez de la
empresa y la alineación organización esponsoreanla actividad
con el negocio ● Creación del documento de
● Estrategia de
gobierno sobre el plan que
● Evaluación del gobierno de IT ciberseguridad
se va a ejecutar
y Seguridad para
● Relevamiento de la implementar ● Creación de indicadores
la prueba de claves de desempeño y
implementación de
penetración métricas
controles de seguridad y
de controles de auditoría
interna
● Relevamiento de los riesgos
relevados, incidentesde
Base de Datos y
regulacionesaplicables a la
industria
UBA FCE –Auditoría y Seguridad de los Sistemas de Información Slide 13
Ejecución de la Prueba de Penetración
Fase de ejecución de la prueba. Requerida para configurar el
ambiente y alinear objetivos de las partes involucradas.
DESCUBRIMIENTO TESTEO REPORTE
Exploración de arquitectura Identificación de potenciales Creación de reportes con
e infraestructura amenazas y vulnerabilidades hallazgos y recomendaciones
Reunión con los Explotación de
stakeholder vulnerabilidades
(si es necesario)
Revisión de Incidentes,
riesgos registrados y
relacionados a procesos de
REMEDIACIÓN Y MEJORA CONTINUA
UBA FCE –Auditoría y Seguridad de los Sistemas de Información Slide 14
Resultados Esperados
1. Entender la madurez de la infraestructura
2. Reporte ejecutivo con recomendaciones
3. Próximos pasos para seguir evolucionando en términos
de seguridad
UBA FCE –Auditoría y Seguridad de los Sistemas de Información Slide 15
Reporte de Ejemplo - (Confidencialidad)
UBA FCE –Auditoría y Seguridad de los Sistemas de Información Slide 16
Algunas herramientas que se pueden utilizar
UBA FCE –Auditoría y Seguridad de los Sistemas de Información Slide 17
Auditoría de código
Una revisión de código, o auditoría, investiga las prácticas de
codificación utilizadas en la aplicación. El objetivo principal de
estas revisiones es descubrir defectos de seguridad y
potencialmente identificar soluciones.
Cada línea de código es un posible punto de entrada
para actores maliciosos. Vulnerabilidades comunes
como la inyección SQL, el cross-site scripting (XSS) y los
desbordamientos de búfer usualmente pueden ser
rastreados hasta pequeños descuidos durante la
codificación.
UBA FCE –Auditoría y Seguridad de los Sistemas de Información Slide 18
Ejemplo de código vulnerable
Código SQL
UBA FCE –Auditoría y Seguridad de los Sistemas de Información Slide 19
Ejemplo de código vulnerable
Código SQL
Escenario de explotación
atacante ingresa los siguientes valores:
Nombre de usuario: admin' --
Contraseña: cualquiercosa
UBA FCE –Auditoría y Seguridad de los Sistemas de Información Slide 20
Ejemplo de código vulnerable
Código SQL
Escenariode explotación -
Consulta no parametrizada
inyección SQL
atacante ingresa los siguientes valores:
Nombre de usuario: admin' --
Contraseña: cualquiercosa
Código SQL resultante
UBA FCE –Auditoría y Seguridad de los Sistemas de Información Slide 21
Ejemplo de código vulnerable
Luego de la identificacion de la vulnerabilidad, para
prevenir la inyección SQL, el codigo es actualizado
para usar una consulta parametrizada, que asegura
que la entrada del usuario se trate como datos en
lugar de como código ejecutable.
UBA FCE –Auditoría y Seguridad de los Sistemas de Información Slide 22
Ejemplo de código vulnerable - inyección SQL
https://www.explainxkcd.com/wiki/index.php/327:_Exploits_of_a_
UBA FCE –Auditoría y Seguridad de los Sistemas de Información Slide 23
Herramientas de SAST (Static Application
Security Testing)
Las herramientas de SAST (Static Application Security Testing) son utilizadas para analizar el
código fuente de una aplicación en busca de vulnerabilidades de seguridad sin ejecutarla.
Estas herramientas ayudan a identificar problemas de seguridad en las fases tempranas del
desarrollo de software. Ejemplos de herramientas de SAST:
UBA FCE –Auditoría y Seguridad de los Sistemas de Información Slide 24
Herramientas de SAST - ejemplos
UBA FCE –Auditoría y Seguridad de los Sistemas de Información Slide 25
Herramientas de SAST - ejemplos
UBA FCE –Auditoría y Seguridad de los Sistemas de Información Slide 26
Herramientas de SAST - ejemplos
UBA FCE –Auditoría y Seguridad de los Sistemas de Información Slide 27
Herramientas de SAST - ejemplos
UBA FCE –Auditoría y Seguridad de los Sistemas de Información Slide 28
Herramientas de SAST – ejemplos de
vulnerabilidades
Inyección SQL
Exposición de
datos sensibles
Cross-Site
Scripting (XSS)
Desbordamiento
de búfer
Bypass de
autorización
UBA FCE –Auditoría y Seguridad de los Sistemas de Información Slide 29
UBA FCE –Auditoría y Seguridad de los Sistemas de Información Slide 30

### Relación con otros temas
*   **Conexión con el marco general:** Este tema se integra con los conceptos de seguridad y auditoría del curso.

---

## Conclusión General: Un Ecosistema Interconectado

Todos estos temas no son silos aislados, sino partes de un ecosistema dinámico y continuo para proteger a la organización:

*   El **Gobierno de TI** establece la dirección estratégica.
*   La **Gestión de Riesgos (ERM)** identifica lo que podría salir mal.
*   **COSO** proporciona el marco para organizar la respuesta a esos riesgos.
*   Los **Controles (ITGC, Procesos, SoD, Awareness)** son la respuesta práctica para mitigar los riesgos y prevenir que las **Amenazas** se materialicen.
*   **DevSecOps** construye defensas desde el inicio en el software.
*   El **BCP** y la **Respuesta a Incidentes** son los planes para actuar cuando los controles fallan.
*   La **Auditoría**, guiada por sus **Normas**, evalúa la eficacia de todo el sistema, proporcionando retroalimentación para la mejora continua.

Comprender la interacción entre estos elementos es fundamental para gestionar la seguridad y el control en el entorno digital actual.

---

## Información Adicional Integrada de la Transcripción

### Perspectivas Prácticas Adicionales

#### Sobre la Implementación de Awareness
La transcripción enfatiza que los programas de concienciación efectivos requieren **repetición y visibilidad constante**, no solo información pasiva. Los ejercicios de phishing simulado son fundamentales como "forma de prueba y aprendizaje" donde el objetivo es educar, no castigar.

#### Sobre la Separación de Ambientes
La transcripción destaca que la separación de ambientes (DEV/QA/PROD) puede ser **física o lógica**, pero es vital para prevenir que los desarrolladores trabajen directamente en producción y asegurar que todos los cambios pasen por un proceso de pruebas y aprobación antes de impactar al negocio.

#### Sobre la Gestión de Accesos Privilegiados
Se enfatiza que **casi todos los componentes de TI tienen cuentas administrativas privilegiadas** y que debido al potencial de abuso o error, se necesitan medidas de seguridad adicionales. La evolución desde "password vaulting" tradicional hacia software especializado de gestión de acceso privilegiado (PAM) refleja la maduración de las prácticas de seguridad.

#### Sobre la Evolución de Metodologías
La transcripción ilustra claramente la evolución desde metodologías waterfall tradicionales (donde la seguridad ocurre tarde y es costosa de corregir) hacia enfoques ágiles y DevSecOps, donde la seguridad se integra desde el inicio como una responsabilidad compartida.

### Conexiones Clave Reforzadas

1. **El Factor Humano como Vector Crítico:** La transcripción refuerza que sin importar cuántas defensas tecnológicas se implementen, el factor humano sigue siendo el vector de ataque inicial más exitoso.

2. **La Interconexión de Controles:** Se evidencia cómo los ITGC no son elementos aislados sino que forman un ecosistema integrado donde la falla en un dominio puede comprometer otros.

3. **La Importancia del Contexto Organizacional:** El gobierno de TI no es solo gestión técnica, sino alineación estratégica que debe considerar todos los stakeholders relevantes.

4. **La Evolución Continua:** Los marcos y prácticas evolucionan constantemente (como COSO 2013, COSO ERM 2017) para adaptarse a entornos de negocio más complejos con avances tecnológicos significativos.

### Reflexión Final

La transcripción proporciona una perspectiva práctica y detallada que complementa perfectamente el marco teórico del resumen original. Destaca que la seguridad y auditoría en el entorno digital actual no son solo cuestiones técnicas, sino que requieren un enfoque holístico que integre personas, procesos y tecnología en un ecosistema dinámico y continuo de protección organizacional.