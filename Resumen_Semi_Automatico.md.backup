<PERSON><PERSON><PERSON>, aquí tienes un resumen completo y ordenado de la información proporcionada, listo para ser estudiado y en formato Markdown.

---

# Resumen de Seguridad de la Información y Principios de Auditoría (UBA FCE)

Este documento resume los temas clave de la materia, estructurados según el glosario provisto, y conectando los conceptos entre sí para una comprensión integral.

## 8. Awareness (Concienciación en Seguridad)

La concienciación es la primera línea de defensa, enfocada en el factor humano, que a menudo es el eslabón más débil en la cadena de seguridad.

### ¿Por qué es Fundamental?
*   El **factor humano** es el vector de ataque inicial más exitoso. La falta de conocimiento o el descuido de un usuario pueden anular defensas tecnológicas robustas.
*   Casos como **LAPSUS$** o el incidente de **Mercado Libre** demuestran que los ataques a menudo explotan la ingeniería social y el robo de credenciales, no necesariamente vulnerabilidades técnicas complejas.
*   **Evidencia práctica:** Sin importar cuántos firewalls, sistemas de detección de intrusiones o herramientas de seguridad sofisticadas se implementen, el factor humano sigue siendo, en la gran mayoría de los casos, el vector de ataque inicial más exitoso.
*   **Realidad actual:** Los grupos criminales cibernéticos logran vulnerar defensas robustas no atacando directamente los sistemas con exploits complejos de día cero, sino aprovechando la ingeniería social, el robo de credenciales o el acceso a través de contratistas.

### Vectores de Ataque Comunes que Explotan al Usuario
*   **Phishing:** Suplantación de identidad (generalmente por email) para engañar a la víctima y que entregue información confidencial (credenciales, datos de tarjetas). Los emails parecen provenir de bancos, servicios de streaming o incluso colegas, dirigiendo a sitios web falsos.
*   **Malware:** Software malicioso (virus, gusanos, troyanos) diseñado para dañar sistemas, robar información o tomar control. Incluye ransomware que cifra datos y exige rescate.
*   **Ingeniería Social:** El arte de manipular a las personas para que realicen acciones o divulguen información confidencial. No siempre involucra tecnología; a veces es solo una llamada telefónica o conversación casual. Es la base de muchos ataques de phishing y malware.
*   **Robo de Identidad:** Asumir la identidad de otra persona para acceder a sus cuentas o realizar acciones maliciosas en su nombre.
*   **Fuga de Datos (Data Breach):** Exposición o robo de información confidencial.
*   **Inteligencia Cibernética:** Recopilación de información sobre un objetivo, la organización o sus empleados, para planificar futuros ataques.

> **Nota importante:** Muchos ataques de malware o ransomware en realidad comienzan con un ataque de ingeniería social dirigido a un empleado. La diversidad de vectores de ataque demuestra por qué la concienciación es tan crítica.

### ¿Cómo Construir un Programa de Awareness Efectivo?
1.  **Comunicación Constante y Creativa:** No basta con comunicados ocasionales. Se deben usar campañas regulares a través de:
    *   Protectores de pantalla en computadoras de oficina
    *   Posters en áreas comunes
    *   Emails internos con mensajes de seguridad periódicos
    *   La idea es mantener la seguridad en la mente de los empleados constantemente

2.  **Ejercicios Prácticos:** Realizar **simulacros de phishing** para medir la reacción de los empleados e identificar áreas de mejora.
    *   Se envían emails simulados que imitan ataques reales para ver cuántos empleados hacen clic en enlaces maliciosos o descargan archivos sospechosos
    *   **El objetivo es educar, no castigar** - es una forma de prueba y aprendizaje
    *   Ayuda a identificar vulnerabilidades en el entrenamiento y áreas donde el mensaje necesita refuerzo

3.  **Capacitación Formal:** Sesiones (online o presenciales) que expliquen los riesgos, las políticas de seguridad y cómo identificar y reportar incidentes.

4.  **Apoyo de la Dirección:** El compromiso de los líderes es crucial para que el personal se tome la seguridad en serio. Si la alta gerencia no muestra compromiso con la seguridad, es muy difícil que el resto del personal la tome en serio. El mensaje de seguridad debe venir de arriba hacia abajo.

5.  **Medición de Impacto:** Evaluar métricas como:
    *   ¿Están disminuyendo las tasas de clics en ejercicios de phishing?
    *   ¿La gente está reportando más emails sospechosos?
    *   ¿Hay menos incidentes relacionados con errores humanos?
    *   Estas métricas ayudan a justificar la inversión en el programa de concienciación y dirigir esfuerzos futuros donde más se necesiten.

### Relación con otros temas
*   **Gestión de Riesgos (ERM):** Reduce directamente el riesgo de que amenazas como el phishing se materialicen.
*   **COSO:** Fortalece el **Ambiente de Control**, uno de los componentes clave del marco.
*   **Amenazas y Ataques:** Es la defensa humana directa contra las técnicas de ingeniería social y phishing.

---

## 9. Gobierno de TI (IT Governance)

El Gobierno de TI es el marco de liderazgo y estructura organizacional que garantiza que la tecnología de la información (TI) respalde y potencie los objetivos estratégicos del negocio.

### Definición y Propósito
*   **Definición:** Conjunto de estructuras, procesos y mecanismos para dirigir y controlar los recursos de TI de forma segura y eficiente.
*   **Propósito:** Asegurar que las inversiones y el uso de la TI creen valor para los stakeholders, optimizando costos y gestionando los riesgos tecnológicos inherentes.

### Diferencia Clave: Gobierno vs. Gestión
Esta es una distinción conceptual muy importante que a menudo se confunde:

*   **Gobierno de TI (Dirección y Supervisión):** Define el **qué** y el **por qué**.
    *   Evalúa las necesidades estratégicas y opciones de los stakeholders
    *   Establece la dirección que debe tomar la función de TI
    *   Toma decisiones sobre prioridades, inversiones, políticas y marcos de control
    *   Monitorea el desempeño de TI, cumplimiento de políticas y logro de objetivos
    *   Es la función de definir qué hacer y por qué, y supervisar que se haga correctamente

*   **Gestión de TI (Ejecución):** Define el **cómo** y el **cuándo**.
    *   Es la función ejecutiva que planifica, construye, opera y monitorea las actividades operativas diarias, semanales o mensuales de la función de TI
    *   Siempre opera bajo la dirección y dentro de las políticas y marcos establecidos por el gobierno de TI
    *   Es la ejecución operacional de la estrategia definida por el gobierno

> **Analogía:** El Gobierno de TI es como el timón que dirige el barco tecnológico de la organización en la dirección correcta y evita las rocas.

### Pilares del Gobierno de TI
1.  **Alineamiento Estratégico:** Asegurar que la TI apoya las metas del negocio.
2.  **Roles y Responsabilidades:** Definir quién toma las decisiones de TI y quién es responsable de qué.
3.  **Gestión de Riesgos:** Identificar, evaluar y tratar los riesgos relacionados con TI.
4.  **Gestión de Recursos:** Optimizar el uso de personas, hardware, software e información.
5.  **Cumplimiento:** Garantizar el acatamiento de leyes, regulaciones y políticas internas.

### Marcos de Referencia
Para implementar este gobierno efectivamente, existen marcos y mejores prácticas útiles:

*   **COBIT (Control Objectives for Information and related Technology):** Es el marco más completo y destacado como modelo de referencia muy comprensivo.
    *   Proporciona un conjunto detallado de procesos clave de TI (34 en su versión anterior, el marco ha evolucionado)
    *   Cubre todo desde planificación y organización hasta entrega y soporte, monitoreo y evaluación
    *   Ayuda a las organizaciones a implementar gobierno de TI estructurando sus procesos y controles

*   **ISO/IEC 38500:** Norma internacional para el gobierno de TI.
*   **AS8-015-2005:** Estándar australiano similar.

### Relación con otros temas
*   **ITGC y Controles de Proceso:** El Gobierno de TI establece las políticas y la estructura que los ITGC y otros controles deben hacer cumplir.
*   **Gestión de Riesgos (ERM):** La gestión de riesgos de TI es un pilar fundamental del Gobierno de TI.
*   **COSO:** El Gobierno de TI es un componente esencial del gobierno corporativo general y del **Ambiente de Control**.

---

## 10 y 14. Controles Generales de TI (ITGC - IT General Controls)

Los ITGC son la base del control tecnológico. No están ligados a una aplicación específica, sino que se aplican a todo el entorno tecnológico (sistemas, redes, aplicaciones).

### ¿Por qué son tan Relevantes para la Auditoría?
Los ITGC crean y soportan el ambiente donde ocurren cosas críticas:

*   **Son la Base Tecnológica:** Si los ITGC son débiles, no se puede confiar en la información generada por los sistemas ni en los controles automáticos que operan dentro de ellos.
    *   Se generan y procesan cálculos importantes
    *   Se producen reportes, muchos de los cuales pueden ser información financiera
    *   Operan controles automatizados dentro de aplicaciones
    *   Se gestiona la seguridad lógica, incluyendo segregación de funciones (SOD)
    *   Funcionan interfaces entre diferentes sistemas

*   **Impacto en Estados Financieros:** Afectan directamente la fiabilidad de la información financiera.
    *   Los ITGC robustos son fundamentales para soportar la confiabilidad del ambiente de TI que impacta la validez de casi todas las aserciones de estados financieros (exactitud, integridad, corte, existencia y ocurrencia, derechos y obligaciones, presentación y revelación)
    *   Un auditor debe evaluarlos para determinar cuánta confianza puede depositar en los sistemas para soportar saldos financieros y operaciones

*   **Eficiencia de la Auditoría:** Si los ITGC son fuertes, el auditor puede reducir la cantidad de pruebas sustantivas (manuales), que son más costosas y consumen más tiempo. Si los ITGC son débiles, el auditor necesitará realizar muchas más pruebas sustantivas manuales.

### Los 4 Dominios Principales de ITGC

#### 1. Acceso a Programas y Datos
*   **Objetivo:** Asegurar que solo las personas autorizadas tengan acceso a los sistemas y datos, y solo con los permisos necesarios para su función (**principio de mínimo privilegio**).
*   **Riesgos Principales:**
    *   Acceso no autorizado a aplicaciones por usuarios de negocio (personal interno)
    *   Acceso no autorizado a aplicaciones y datos por usuarios de TI (personal técnico que gestiona los sistemas)
    *   Cambios no autorizados en los datos

*   **Capas de Seguridad:** La seguridad de información y administración de accesos se gestiona y monitorea a través de múltiples capas:
    *   Seguridad en el perímetro de red (frontera con el mundo exterior)
    *   Red interna
    *   Sistema operativo de servidores
    *   Los datos mismos (bases de datos, archivos, etc.)
    *   Aplicaciones de negocio

*   **Controles Clave:**
    *   Proceso formal de alta, baja y modificación de usuarios (ABM de usuarios), con solicitudes y autorizaciones documentadas
    *   Revisión periódica de los accesos concedidos para asegurar que sigan siendo apropiados
    *   Controles estrictos sobre cuentas privilegiadas (administradores, superusuarios) y genéricas
    *   Implementación de controles de **Segregación de Funciones (SoD)** para evitar conflictos de roles
    *   Control riguroso sobre cuentas privilegiadas o genéricas dentro de aplicaciones

### Controles Específicos por Tipo de Seguridad

#### Seguridad de Aplicaciones (Para usuarios de negocio y superusuarios)
**Controles Preventivos:**
*   Diseño apropiado de roles y perfiles de usuario con permisos mínimos necesarios (principio de menor privilegio)
*   Proceso formal y documentado de aprovisionamiento de usuarios donde el acceso se solicita, autoriza y otorga de manera controlada
*   Implementación de controles compensatorios cuando no se pueden evitar conflictos de SOD
*   Restricción y control estricto del acceso para terceros (consultores o proveedores)

**Controles Detectivos:**
*   Análisis periódico de roles y permisos asignados a usuarios para identificar conflictos de SOD existentes
*   Procesos para identificar y remediar estos conflictos una vez detectados
*   Monitoreo de la actividad de usuarios con permisos elevados o funciones críticas dentro de la aplicación

#### Seguridad de Datos (Para personal técnico como DBAs)
**Controles Clave:**
*   Todas las solicitudes de acceso a bases de datos o archivos sensibles deben ser apropiadamente revisadas y autorizadas
*   Proceso eficiente y oportuno para remover acceso de personal que ha dejado la organización
*   Monitoreo periódico de transacciones y actividades realizadas por superusuarios o administradores de base de datos para detectar actividades sospechosas o no autorizadas
*   Aplicación de estándares robustos de seguridad de base de datos y políticas de contraseñas fuertes

#### Seguridad del Sistema Operativo
**Controles Fundamentales:**
*   Restricción del acceso a cuentas privilegiadas del SO (como administrador en Windows o Root en sistemas Unix) solo al personal técnico estrictamente necesario
*   Monitoreo periódico de la actividad de estos superusuarios o administradores del SO para identificar acciones inusuales
*   Aplicación de estándares robustos de seguridad y políticas de contraseñas fuertes a los sistemas operativos mismos

#### Administración de Accesos Diferenciada

**Para Usuarios de Negocio:**
*   El acceso debe ser formalmente solicitado por personal autorizado (usualmente un gerente de área de negocio) y otorgado por una función separada dentro de TI
*   Revisiones periódicas del acceso otorgado para asegurar que sigue siendo apropiado y necesario
*   Remoción de acceso para personal que se va debe ser oportuna, idealmente coordinada con el proceso de desvinculación del empleado
*   Cualquier incidente de seguridad relacionado con acceso debe ser identificado e investigado

**Para Usuarios Técnicos:**
*   Casi todos los componentes de TI tienen cuentas administrativas privilegiadas (administrador de dominio Windows, Root Unix, QSECOFR en OS 400, DBA en SQL Server u Oracle, administradores de firewall, etc.)
*   Debido al potencial de abuso o error con estas cuentas, se necesitan medidas de seguridad adicionales
*   **Gestión de Contraseñas Privilegiadas:** Tradicionalmente se usaba "password vaulting" (almacenar la contraseña maestra en un sobre sellado accedido solo en emergencias), aunque hoy esto se gestiona más efectivamente con software especializado de gestión de acceso privilegiado (PAM)
*   La asignación de acceso a estas cuentas debe ser específica y basada en roles, evitando credenciales compartidas

#### 2. Cambios a Programas (y Desarrollo)
*   **Objetivo:** Garantizar que solo los cambios autorizados, probados y correctos se implementen en el entorno de producción.
*   **Riesgos Específicos:**
    *   Implementar cambios que no fueron solicitados por el negocio o que introducen errores en aplicaciones
    *   Hacer cambios directos no autorizados a programas en producción o su configuración
    *   Aplicar actualizaciones inadecuadas del sistema operativo o base de datos que afecten estabilidad o seguridad
    *   Implementar nuevos sistemas o módulos incorrectamente debido a errores de codificación, errores de configuración, o incluir desarrollos no autorizados
    *   Errores durante migración o conversión de datos (transaccionales o maestros) como parte de un cambio o implementación

*   **Separación de Ambientes (Fundamental):**
    *   **Desarrollo (DEV):** Donde los programadores escriben y prueban inicialmente el código
    *   **Pruebas/QA:** Donde los cambios se someten a pruebas más rigurosas (funcionales, de rendimiento, seguridad), a menudo por personal diferente a los desarrolladores
    *   **Producción (PROD):** Lo que usan los usuarios de negocio para sus operaciones diarias
    *   Esta separación (física o lógica) es vital para prevenir que desarrolladores trabajen directamente en producción

*   **Controles Clave:**
    *   Proceso formal de gestión de cambios: solicitud, análisis, diseño, pruebas (incluyendo pruebas de aceptación del usuario - UAT) y aprobación formal antes del pase a producción
    *   Segregación de funciones: El personal de desarrollo no debe tener permisos para mover cambios a producción
    *   Verificar que cada cambio fue formalmente solicitado y cumple especificaciones
    *   Verificar que se generó un documento de diseño funcional aprobado
    *   Verificar que el cambio fue probado exhaustivamente en el ambiente de pruebas/QA
    *   Verificar que el cambio tiene la autorización formal requerida antes de moverse al ambiente de producción

#### 3. Operaciones de Cómputo
*   **Objetivo:** Asegurar que los sistemas que soportan procesos clave de negocio funcionen correcta y continuamente. Son responsables de asegurar que los datos de producción de la empresa se almacenen, preserven y transfieran con integridad y exactitud.
*   **Riesgos:** Interrupción de los servicios, pérdida de datos, procesamiento incorrecto de transacciones.
*   **Tres Áreas Principales:**
    1. **Procesamiento Automatizado de Información:** Actividades de procesamiento de transacciones que el sistema realiza automáticamente
    2. **Gestión y Resolución de Problemas:** Cómo se identifican, gestionan y corrigen fallas o errores
    3. **Operaciones del Centro de Datos:** Actividades relacionadas con la infraestructura física y lógica del centro de datos

*   **Controles Clave por Área:**

    **Procesamiento de Información:**
    *   Programación y monitoreo de procesos batch (como cálculo de nómina o procesamiento nocturno de transacciones)
    *   Procesamiento en tiempo real (transacciones procesadas inmediatamente, como una transacción de punto de venta)
    *   Procesamiento de interfaces (transferencia automatizada de datos entre diferentes sistemas)
    *   Monitoreo general del procesamiento de transacciones para detectar anomalías o errores

    **Gestión de Problemas:**
    *   Identificación de problemas o incidentes técnicos
    *   Monitoreo de redes y sistemas para detectar fallas de rendimiento o disponibilidad
    *   Funciones de Help Desk/Service Desk (primer punto de contacto para usuarios que reportan problemas)
    *   Escalamiento basado en niveles (Nivel 1 para problemas básicos, Nivel 2 y 3 para problemas técnicos más complejos que requieren especialistas)

    **Operaciones del Centro de Datos:**
    *   Gestión de backups y proceso de recuperación de información, restauración de datos
    *   Existencia e implementación de plan de recuperación ante desastres (DRP) y plan de continuidad del negocio (BCP)
    *   Protección ambiental del centro de datos (control de temperatura y humedad, sistemas de supresión de incendios, seguridad física)
    *   Gestión de capacidad para asegurar que la infraestructura de TI tenga recursos suficientes para soportar operaciones actuales y futuras del negocio

### Controles Específicos por Tipo de Actividad Operacional

#### Controles Gerenciales
*   Tener políticas, estándares y procedimientos claros para actividades de monitoreo y operacionales
*   Definir roles y responsabilidades claros, donde nuevamente la segregación de funciones y gestión crítica de accesos son relevantes
*   Establecer controles generales de monitoreo sobre infraestructura y procesos

#### Controles para Procesamiento Batch
*   Procesos definidos para agregar, remover y modificar trabajos batch
*   Procedimientos de recuperación probados para cada trabajo en caso de interrupción
*   Monitoreo constante de la ejecución de estos procesos batch para asegurar que se completen correcta y oportunamente
*   Controles robustos de acceso sobre la herramienta que gestiona y monitorea estos trabajos

#### Controles para Procesamiento en Tiempo Real
*   Los aspectos de configuración deben ser controlados, especialmente para transacciones complejas o aquellas que usan middleware
*   Es crucial definir cómo se capturan los errores que ocurren durante el procesamiento y cómo se generan alertas para corrección oportuna
*   Controles de acceso para quién puede modificar la configuración de estas transacciones críticas en tiempo real

#### Controles para Backups y Gestión de Problemas
*   Debe asegurarse que el contenido de los backups y su frecuencia estén alineados con los objetivos de continuidad del negocio, específicamente el RPO (Recovery Point Objective - la pérdida máxima tolerable de datos)
*   La empresa necesita certeza de que los backups estarán disponibles y accesibles cuando se necesiten
*   Debe tener un proceso documentado y, crucialmente, **probado** para restaurar o recuperar información de esos backups

> **Nota crítica:** Este dominio de operaciones es la columna vertebral tecnológica que mantiene todo funcionando. Si falla, el negocio se detiene.

### Relación con otros temas
*   **COSO:** Son la materialización de las **Actividades de Control** en el dominio de TI.
*   **BCP/DRP:** El dominio de Operaciones (backups, DRP) es el soporte técnico del BCP.
*   **Procesos de Negocio:** Unos ITGC sólidos son necesarios para poder confiar en los controles de aplicación.

---

## 11. Gestión de Riesgos (ERM - Enterprise Risk Management)

ERM es un proceso integral para identificar, evaluar y responder a los riesgos potenciales que podrían afectar el logro de los objetivos de una organización.

### Conceptos Fundamentales
*   **Riesgo:** Posibilidad de que ocurra un evento con un impacto negativo en los objetivos. Se mide por **probabilidad** e **impacto**.
*   **Marco de Referencia:** **COSO ERM** es el modelo principal. Su versión de 2017 ("Integrando con Estrategia y Desempeño") enfatiza la gestión de riesgos como una herramienta para crear y preservar valor.

### Conceptos Clave para la Toma de Decisiones
Estos son conceptos clave que guían la toma de decisiones relacionadas con riesgos:

*   **Apetito de Riesgo:** La cantidad de riesgo que una organización está dispuesta a aceptar de forma estratégica para alcanzar sus objetivos. Definir el apetito de riesgo ayuda a alinear la estrategia, estructura organizacional y procesos.
    *   **Factores a considerar:** Modelo de negocio, perfil de riesgo actual de la organización, su cultura, riesgos inherentes de su industria, necesidad de equilibrar riesgo con oportunidad de retornos
    *   **Categorías de apetito:**
        - **Intolerante:** Muy cauteloso, baja disposición a la incertidumbre
        - **Adverso:** Moderado, acepta riesgos justificados y monitoreables
        - **Tolerante:** Predispuesto al riesgo para beneficios a largo plazo
        - **Propenso:** Flexible, dispuesto a asumir más riesgo para altos retornos con mayor tolerancia a fallas potenciales

*   **Tolerancia al Riesgo:** Más específica. Es la cantidad máxima de variación o desviación que una organización está dispuesta a aceptar alrededor del logro de un objetivo específico, o la cantidad máxima de riesgo que está dispuesta a aceptar para un riesgo individual particular. Sirve como umbral de alerta - si un riesgo excede la tolerancia definida, se debe tomar acción.

*   **Capacidad de Riesgo:** La cantidad total y tipo de riesgo que una organización es capaz de soportar sin poner en peligro su existencia o capacidad de continuar operando. Es el límite máximo absoluto. La tolerancia al riesgo para un evento específico siempre debe estar por debajo de la capacidad general de riesgo de la organización.

### Proceso de Gestión de Riesgos
1.  **Identificación de Riesgos:** ¿Qué puede salir mal? Se identifican riesgos en diversas categorías (estratégicos, financieros, operativos, de cumplimiento, de TI, etc.).

2.  **Evaluación de Riesgos:** Se analiza la **probabilidad** y el **impacto** de cada riesgo usando el método clásico del **mapa de riesgo inherente**. Es una matriz que grafica la probabilidad de que ocurra el riesgo (baja, media, alta) contra el impacto o severidad de sus consecuencias (baja, media, alta). Los riesgos se ubican en esta matriz para visualizarlos antes de aplicar cualquier control o respuesta. Los riesgos en la esquina superior derecha (alta probabilidad y alto impacto) son los más críticos.

3.  **Respuesta al Riesgo:** Se definen estrategias para llevar el **riesgo inherente** (riesgo bruto antes de controles) a un nivel de **riesgo residual** (riesgo después de controles) aceptable, idealmente dentro del apetito de riesgo. Las cuatro estrategias principales son:
    *   **Evitar:** Tomar acciones para que el riesgo simplemente no exista (ej. decidir no emprender una actividad que genera el riesgo, eliminar las causas)
    *   **Reducir/Mitigar:** Implementar controles y acciones para disminuir la probabilidad de que ocurra el riesgo o para reducir el impacto si ocurre (reducir a valores aceptables). Esta es la estrategia donde se aplican la mayoría de los controles internos.
    *   **Transferir/Compartir:** Trasladar parte del riesgo a un tercero (el ejemplo más común es contratar un seguro)
    *   **Aceptar:** Decidir no tomar ninguna acción para modificar el riesgo. Típicamente se hace para riesgos con baja probabilidad e impacto marginal, donde el costo de mitigación supera el beneficio esperado.

4.  **Monitoreo:** Revisar continuamente los riesgos y la efectividad de las respuestas. La gestión efectiva de riesgos es un ciclo continuo de identificar, evaluar, responder y monitorear riesgos, asegurando que el riesgo residual se mantenga dentro de la tolerancia y capacidad de la organización.

### Relación con otros temas
*   **COSO:** La evaluación de riesgos es uno de los cinco componentes del marco COSO.
*   **Controles (ITGC y de Proceso):** Son la principal herramienta para **mitigar** los riesgos identificados.
*   **BCP:** Es la respuesta específica para los riesgos de interrupción del negocio.
*   **Auditoría:** Evalúa si la organización gestiona adecuadamente sus riesgos y si los controles son efectivos.

---

## 12. COSO (Marco Integrado de Control Interno)

COSO es el marco de referencia globalmente aceptado para diseñar, implementar y evaluar la efectividad de un sistema de control interno.

### Definición y Objetivos
*   **Definición:** Un proceso, llevado a cabo por toda la organización, diseñado para proporcionar una **seguridad razonable** (no absoluta) sobre el logro de los objetivos.
*   **Tres Categorías de Objetivos:**
    1.  **Operacionales:** Eficacia y eficiencia de las operaciones.
    2.  **De Reporte (Reporting):** Fiabilidad de la información (financiera y no financiera).
    3.  **De Cumplimiento:** Adhesión a las leyes y regulaciones aplicables.

### Los 5 Componentes del Marco COSO (y sus 17 Principios)

#### 1. Ambiente de Control (Principios 1-5)
Es la base de todo el sistema. Define el "tono en la cima" de la organización e influye la conciencia de control de su gente. Incluye integridad, valores éticos, filosofía de la gerencia, estructura organizacional, asignación de autoridad y responsabilidad, y políticas y prácticas de recursos humanos.

**Principios:**
*   La organización demuestra compromiso con integridad y valores éticos
*   El Consejo de Administración demuestra independencia de la gerencia y ejerce supervisión del desarrollo y desempeño del control interno
*   La gerencia establece, con supervisión del consejo, estructuras, líneas de reporte y autoridades y responsabilidades apropiadas en la búsqueda de objetivos
*   La organización demuestra compromiso para atraer, desarrollar y retener individuos competentes en alineación con objetivos
*   La organización responsabiliza a individuos por sus responsabilidades de control interno en la búsqueda de objetivos

> **Crucial:** Un ambiente de control débil o falta de ética en la cima socava todos los demás controles.

#### 2. Evaluación de Riesgos (Principios 6-9)
La identificación y análisis de riesgos relevantes para lograr los objetivos predefinidos. Sirve como base para determinar cómo deben gestionarse los riesgos. Integra directamente el proceso de gestión de riesgos, incluyendo consideración de fraude y cambios relevantes.

#### 3. Actividades de Control (Principios 10-12)
Las políticas y procedimientos que la gerencia establece para asegurar que se tomen las acciones necesarias para mitigar riesgos identificados y asegurar que se cumplan los objetivos. Abarcan una amplia gama de acciones como aprobaciones, autorizaciones, verificaciones, reconciliaciones, salvaguarda de activos y segregación de funciones.

> **Conexión clave:** Aquí es donde encajan perfectamente los ITGC y los Controles de Proceso/SOD - son la materialización de estas actividades de control.

#### 4. Información y Comunicación (Principios 13-15)
Se refiere a la necesidad de identificar, capturar y comunicar información relevante de manera oportuna para permitir al personal llevar a cabo sus responsabilidades de control.

#### 5. Actividades de Monitoreo (Principios 16-17)
Evaluaciones (continuas o puntuales) para asegurar que los componentes del control interno están presentes y funcionando. La auditoría interna es un ejemplo clave de monitoreo.

### Limitaciones Inherentes del Control Interno
*   No puede dar seguridad absoluta.
*   Está sujeto a errores humanos.
*   Puede ser eludido por la **colusión** (acuerdo entre dos o más personas).
*   La dirección puede anular los controles (**management override**).

### Relación con otros temas
*   **Es el Marco Integrador:** COSO engloba todos los demás conceptos. La **Evaluación de Riesgos** se basa en ERM. Las **Actividades de Control** incluyen los ITGC y controles de proceso/SoD. El **Gobierno de TI** es parte del Ambiente de Control. La **Auditoría** utiliza COSO como modelo para evaluar el control interno.

---

## 13. Plan de Continuidad del Negocio (BCP)

El BCP es el conjunto de planes y estrategias que permiten a una organización seguir operando sus funciones críticas durante y después de un evento disruptivo.

### Conceptos Clave
*   **BCP (Business Continuity Plan):** El plan general y proactivo que cubre la continuidad de todo el negocio (procesos, personas, tecnología).
*   **BIA (Business Impact Analysis):** El análisis previo que identifica los procesos de negocio más críticos y el impacto (financiero y operacional) de su interrupción en el tiempo.
*   **DRP (Disaster Recovery Plan):** El plan reactivo y enfocado en TI. Es parte del BCP y detalla cómo recuperar la infraestructura tecnológica y los datos tras un desastre.

### Métricas Fundamentales (Definidas en el BIA)
*   **RTO (Recovery Time Objective):** Tiempo máximo tolerable para restaurar un proceso o sistema después de una interrupción. Define **cuán rápido** debe recuperarse.
*   **RPO (Recovery Point Objective):** Período máximo tolerable de pérdida de datos. Define **a qué punto en el tiempo** se debe recuperar la información (ej. al último backup).

### Proceso
1.  **BIA:** Identificar procesos críticos y definir sus RTO y RPO.
2.  **Estrategia de Continuidad:** Definir cómo se mantendrán esos procesos (ej. sitio alternativo, trabajo remoto).
3.  **Desarrollo del Plan (BCP/DRP):** Documentar los procedimientos, roles y recursos necesarios.
4.  **Pruebas y Mantenimiento:** Probar el plan regularmente (ej. simulacros de recuperación) para asegurar que funcione y mantenerlo actualizado.

### Relación con otros temas
*   **Gestión de Riesgos (ERM):** Es la respuesta directa a los riesgos de interrupción de operaciones de alto impacto.
*   **ITGC (Operaciones):** El DRP depende críticamente de los controles de operaciones, como los backups y los procedimientos de restauración.
*   **Amenazas y Ataques:** Un ciberataque exitoso es una de las principales amenazas que pueden activar un BCP/DRP.

---

## 15. Controles en Procesos de Negocio y Segregación de Funciones (SoD)

Estos controles, también llamados "controles de aplicación", operan a nivel de transacción dentro de un proceso de negocio específico. Su objetivo es asegurar la integridad de los registros.

### Ciclos de Negocio y Controles Típicos

#### Ciclo de Compras (Purchase-to-Pay)
*   **Subprocesos:** Pedido de compra, recepción de mercadería, procesamiento de facturas, pago.
*   **Riesgos:** Pagar por bienes no recibidos, pagar a proveedores ficticios, pagos duplicados.
*   **Controles Clave:**
    *   **3-Way Match:** Comparación de la Orden de Compra, la Recepción y la Factura antes de pagar.
    *   Aprobación de órdenes de compra.
    *   Reconciliaciones bancarias.

#### Ciclo de Ventas (Order-to-Cash)
*   **Subprocesos:** Toma de pedido, envío, facturación, cobranza.
*   **Riesgos:** Facturar incorrectamente, no facturar envíos, problemas de crédito de clientes.
*   **Controles Clave:**
    *   Validación del crédito del cliente antes de aceptar el pedido.
    *   Reconciliación entre envíos y facturas.
    *   Autorización de notas de crédito.

### Segregación de Funciones (SoD - Segregation of Duties)
*   **Principio Fundamental:** Ninguna persona debe tener la capacidad de ejecutar y ocultar un error o fraude por sí sola. Se deben separar las tareas incompatibles.
*   **Beneficios:** Reduce el riesgo de error y fraude, y actúa como un elemento disuasorio.
*   **Ejemplos de Funciones Incompatibles:**
    *   Crear un proveedor y procesar pagos a ese proveedor.
    *   Registrar una venta y autorizar la nota de crédito para esa venta.
    *   Tener la custodia de un activo y acceso a los registros contables de ese activo.
*   **Controles Compensatorios:** Controles alternativos (generalmente una revisión por parte de un supervisor) que se implementan cuando la SoD completa no es factible, para mitigar el riesgo.

### Relación con otros temas
*   **COSO:** Son el ejemplo perfecto de **Actividades de Control** a nivel transaccional.
*   **ITGC:** Los controles de aplicación dependen de un entorno tecnológico fiable, garantizado por los ITGC.
*   **Auditoría:** La evaluación de los controles de proceso y la SoD es un foco central de la auditoría de procesos y financiera.

---

## 16. Normas de Auditoría

Son el marco regulatorio que asegura la calidad, independencia y consistencia del trabajo de auditoría.

### Tipos de Normas
1.  **Normas de Auditoría Externa (Locales e Internacionales):**
    *   **RT 53 (Argentina - FACPCE):** Regula la auditoría de estados financieros. Define etapas (Planificación, Ejecución, Conclusión) y principios clave.
    *   **ISA (International Standards on Auditing):** Emitidas por el IAASB. Son las normas internacionales que buscan la convergencia global. La **ISA 315** es clave, ya que exige al auditor comprender la entidad y sus riesgos, incluyendo los de TI.
2.  **Normas de Auditoría Interna:**
    *   **Normas Globales de Auditoría Interna del IIA (2024):** Emitidas por el Institute of Internal Auditors (IIA). Establecen los principios para la práctica profesional de la auditoría interna. Se estructuran en 5 dominios: Propósito, Ética y Profesionalismo, Gobernanza, Gestión y Desempeño.

### Principios Fundamentales de Auditoría
*   **Independencia:** El auditor debe ser independiente en mente y apariencia.
*   **Escepticismo Profesional:** Una actitud mental crítica y de cuestionamiento. No asumir que todo está bien sin evidencia.
*   **Evidencia de Auditoría Suficiente y Apropiada:** Base para las conclusiones del auditor.
*   **Documentación:** El trabajo del auditor debe estar debidamente documentado en papeles de trabajo.

### Tipos de Opinión (Auditoría Externa)
*   **No Modificada (Limpia):** Los estados financieros presentan razonablemente la realidad.
*   **Modificada:**
    *   **Con Salvedades:** Presentan razonablemente, *excepto por* un tema específico.
    *   **Adversa:** No presentan razonablemente la realidad.
    *   **Abstención de Opinión:** El auditor no pudo obtener evidencia para opinar.

### Relación con otros temas
*   **Son el "Reglamento":** Definen cómo el auditor debe planificar y ejecutar su trabajo (el siguiente tema) para evaluar todos los demás conceptos (COSO, ITGC, ERM, etc.).

---

## 17. Planificación, Ejecución y Pruebas de Auditoría

Describe el proceso práctico que sigue un auditor para realizar su trabajo.

### Etapas del Proceso
1.  **Planificación:**
    *   Entender la entidad, su entorno, sistemas y control interno (COSO).
    *   Evaluar los riesgos (ERM) para identificar áreas de foco.
    *   Definir el alcance, la estrategia y los procedimientos de auditoría.
2.  **Ejecución:**
    *   Realizar las pruebas planificadas para obtener evidencia.
3.  **Conclusión e Informe:**
    *   Evaluar la evidencia obtenida.
    *   Formar una opinión o conclusión.
    *   Emitir un informe con los hallazgos.

### Tipos de Pruebas de Auditoría
*   **Pruebas de Controles:** Se realizan para evaluar la **eficacia operativa** de un control. El objetivo es ver si el control funcionó como se diseñó a lo largo del período.
*   **Pruebas Sustantivas:** Se realizan para detectar errores materiales en los saldos de los estados financieros. Pueden ser:
    *   **Procedimientos Analíticos:** Análisis de tendencias y ratios.
    *   **Pruebas de Detalle:** Examen de transacciones y saldos individuales.

### El "Ciclo de Confianza" y el Walkthrough
*   Para confiar en los controles de una entidad (y así reducir las pruebas sustantivas), el auditor sigue un ciclo:
    1.  **Entender** el control.
    2.  **Evaluar su diseño** (¿es adecuado para mitigar el riesgo?).
    3.  **Probar su eficacia operativa** (¿funcionó en la práctica?).
*   El **Walkthrough (Prueba de Recorrido)** es la técnica clave para el paso 1 y 2. Consiste en seguir una única transacción de principio a fin a través del proceso para confirmar el entendimiento y evaluar el diseño de los controles.

### Documentación del Entendimiento
*   Se utilizan herramientas como **narrativas** (descripciones escritas), **diagramas de flujo** (representaciones visuales) y **matrices de riesgo y control**.

### Relación con otros temas
*   **Es la Práctica:** Es la aplicación de las **Normas de Auditoría** para evaluar el **Control Interno (COSO)**, los **ITGC** y los **Controles de Proceso**. La **Gestión de Riesgos (ERM)** guía la planificación.

---

## 18. Seguridad en el SDLC y DevSecOps

Este tema se centra en construir seguridad en el software desde su concepción, en lugar de añadirla al final.

### Conceptos Clave
*   **SDLC (Software Development Lifecycle):** El proceso estructurado para diseñar, desarrollar, probar y mantener software.

*   **Metodologías de Desarrollo:**
    *   **Tradicional/Waterfall:** Sigue fases secuenciales (iniciación del proyecto, análisis de requerimientos y diseño, construcción/codificación, pruebas y QA, conversión de datos, implementación final). En este modelo lineal, las actividades rigurosas de seguridad y pruebas a menudo ocurren tarde en el ciclo, haciendo costoso y difícil corregir problemas encontrados posteriormente.
    *   **Ágil/Scrum:** Marco para gestionar proyectos complejos usando un enfoque iterativo e incremental. Elementos clave:
        - **Roles:** Scrum Master (facilita el equipo), Product Owner (representa el negocio y define el "qué"), Equipo de Desarrollo (hace el "cómo")
        - **Artefactos:** Product Backlog (lista priorizada de características), Sprint Backlog (tareas para un ciclo corto o sprint), Definición de Terminado (define cuándo algo está completo)
        - **Eventos:** Sprint Planning (planificar el sprint), Daily Scrum (sincronización diaria), Sprint Review (presentar trabajo terminado), Sprint Retrospective (mejorar el proceso)

*   **Shift Left:** La práctica de mover las actividades de seguridad lo más temprano posible en el ciclo de desarrollo. Es más barato y efectivo corregir fallos en las etapas iniciales.

*   **DevSecOps:** Una cultura y un conjunto de prácticas que integran la seguridad (**Sec**) de forma automatizada en el ciclo de vida de DevOps (Desarrollo - **Dev** y Operaciones - **Ops**).
    *   Mientras que en un modelo ágil puro, la seguridad podría ocurrir después del despliegue en un ciclo continuo, DevSecOps integra la seguridad en cada fase del ciclo de vida desde la planificación inicial hasta la operación continua
    *   La seguridad no es un paso final, es una responsabilidad compartida e integrada de todos, no de un equipo aislado

### Herramientas de Seguridad Automatizada en DevSecOps
*   **SAST (Static Application Security Testing):** Analiza el código fuente en busca de vulnerabilidades sin ejecutarlo.
*   **DAST (Dynamic Application Security Testing):** Prueba la aplicación mientras se está ejecutando, simulando ataques externos.
*   **SCA (Software Composition Analysis):** Analiza las librerías de terceros y componentes de código abierto utilizados en el proyecto para identificar vulnerabilidades conocidas.
*   **Fuzzing:** Envía datos inválidos, inesperados o aleatorios a una aplicación para provocar fallos.

### Controles Específicos para Cambios de Programas
Para mitigar riesgos en el proceso de cambios, los controles incluyen:

*   **Controles Preventivos:**
    *   Mantener separación adecuada de ambientes (DEV/QA/PROD)
    *   Asegurar segregación apropiada de funciones (ej. prevenir que personal de desarrollo tenga permisos para mover cambios a producción)
    *   Verificar que cada cambio fue formalmente solicitado y cumple especificaciones
    *   Verificar que se generó un documento de diseño funcional aprobado
    *   Verificar que el cambio fue probado exhaustivamente en el ambiente de pruebas/QA, incluyendo pruebas de aceptación del usuario
    *   Verificar que el cambio tiene la autorización formal requerida antes de moverse al ambiente de producción
    *   Si el cambio involucra migración de datos, verificar que fue probado y documentado

*   **Proceso Formal:** Las organizaciones deben tener un proceso formal, documentado y definido para desarrollo y cambios de programas. Esto asegura que se sigan los pasos requeridos y que los controles estén presentes en cada etapa.

### Modelo de Amenazas STRIDE
Es un modelo para clasificar las amenazas a la seguridad del software:
*   **S**poofing (Suplantación) -> Mitigado por **Autenticación**.
*   **T**ampering (Manipulación) -> Mitigado por **Integridad**.
*   **R**epudiation (Repudio) -> Mitigado por **No Repudio**.
*   **I**nformation Disclosure (Divulgación de Información) -> Mitigado por **Confidencialidad**.
*   **D**enial of Service (Denegación de Servicio) -> Mitigado por **Disponibilidad**.
*   **E**levation of Privilege (Elevación de Privilegios) -> Mitigado por **Autorización**.

### Relación con otros temas
*   **ITGC (Cambios):** Es la implementación de controles de seguridad dentro de este dominio.
*   **Amenazas y Ataques:** Busca construir software que sea inherentemente resistente a estas amenazas.
*   **Gestión de Riesgos (ERM):** Es una respuesta proactiva para mitigar los riesgos asociados a vulnerabilidades en el software.

---

## 19. Tipos de Amenazas y Ataques

Este tema detalla los peligros y las técnicas que los actores maliciosos utilizan para comprometer los sistemas.

### Conceptos Clave
*   **Amenaza:** Cualquier circunstancia o evento con el potencial de causar daño.
*   **Superficie de Ataque:** El conjunto total de puntos de entrada que un atacante podría usar para comprometer un sistema.
*   **IOC (Indicator of Compromise):** Evidencia forense que indica que un ataque ha ocurrido (ej. un hash de malware conocido, una IP maliciosa).
*   **IOA (Indicator of Attack):** Evidencia que indica que un ataque está en progreso (ej. un movimiento lateral en la red).

### Tipos de Malware
*   **Ransomware:** Cifra los datos de la víctima y exige un rescate para restaurarlos.
*   **Spyware:** Espía las actividades del usuario sin su conocimiento.
*   **Trojan (Troyano):** Se disfraza de software legítimo pero contiene una carga maliciosa.
*   **Worm (Gusano):** Se replica y propaga a través de redes sin intervención del usuario.

### Ataques Más Frecuentes
*   **Ransomware:** Uno de los ataques más dañinos y lucrativos.
*   **Phishing:** La puerta de entrada más común, basada en ingeniería social.
*   **Fuga de Datos (Data Breach):** El robo o exposición de información sensible.
*   **Ingeniería Social:** La manipulación psicológica de las personas. Es la base de muchos otros ataques.

### Marco de Referencia
*   **MITRE ATT&CK:** Una base de conocimiento global de tácticas y técnicas de adversarios basadas en observaciones del mundo real. Es un "mapa" de cómo operan los atacantes.

### Relación con otros temas
*   **Son el "Enemigo":** Son los eventos adversos que **ERM** busca gestionar y que los **controles (ITGC, Awareness, SDLC)** buscan prevenir.
*   **BCP/DRP:** Se activan cuando una de estas amenazas causa una interrupción mayor.
*   **Detección y Respuesta:** Se encarga de identificar y manejar los incidentes cuando una amenaza se materializa.

---

## 20. Detección y Respuesta a Incidentes de Seguridad

*Nota: Este tema no fue desarrollado en detalle en la información fuente, por lo que se presenta un resumen conceptual.*

A pesar de los controles preventivos, es inevitable que ocurran incidentes. La capacidad de una organización para detectarlos rápidamente y responder de manera efectiva es crucial.

### Proceso
1.  **Detección:** Monitorear sistemas y redes en busca de actividad sospechosa (basado en IOCs y IOAs). Esto es a menudo realizado por un **SOC (Security Operations Center)**.
2.  **Respuesta:** Un equipo especializado (**CSIRT - Computer Security Incident Response Team**) toma acción. Las fases típicas son:
    *   **Contención:** Aislar el sistema afectado para que el incidente no se propague.
    *   **Erradicación:** Eliminar la causa raíz del incidente (ej. el malware).
    *   **Recuperación:** Restaurar los sistemas a un estado seguro y operativo.
    *   **Lecciones Aprendidas:** Analizar el incidente para mejorar las defensas futuras.

### Relación con otros temas
*   **Es el Plan Reactivo:** Actúa cuando los controles preventivos fallan.
*   **Amenazas y Ataques:** Es la respuesta directa a la materialización de una amenaza.
*   **BCP/DRP:** La fase de recuperación de un incidente mayor a menudo se alinea con los procedimientos del DRP.

---

## 21. Pruebas de Penetración y Auditoría de Código

*Nota: Este tema no fue desarrollado en detalle en la información fuente, por lo que se presenta un resumen conceptual.*

Son técnicas proactivas de evaluación de seguridad para encontrar debilidades antes de que lo hagan los atacantes.

### Técnicas
*   **Pruebas de Penetración (Pentesting):**
    *   Un ataque simulado y autorizado contra un sistema para identificar vulnerabilidades explotables.
    *   Utiliza diferentes enfoques según el nivel de conocimiento del "atacante":
        *   **Black Box:** Sin conocimiento previo del sistema.
        *   **White Box:** Con conocimiento total (código fuente, arquitectura).
        *   **Gray Box:** Con conocimiento parcial (ej. credenciales de usuario estándar).
*   **Auditoría de Código (Code Review):**
    *   Una revisión manual o automatizada del código fuente de una aplicación para identificar fallos de seguridad, lógica y programación insegura. Está directamente relacionada con las herramientas **SAST**.

### Relación con otros temas
*   **SDLC y DevSecOps:** Son técnicas de verificación y validación que se integran en el ciclo de desarrollo para asegurar la calidad del software.
*   **Auditoría:** Pueden ser realizadas por auditores de seguridad o el auditor puede revisar que la organización las realice periódicamente y gestione los hallazgos.
*   **Gestión de Riesgos (ERM):** Ayudan a identificar y validar la existencia de riesgos técnicos.

---

## Conclusión General: Un Ecosistema Interconectado

Todos estos temas no son silos aislados, sino partes de un ecosistema dinámico y continuo para proteger a la organización:

*   El **Gobierno de TI** establece la dirección estratégica.
*   La **Gestión de Riesgos (ERM)** identifica lo que podría salir mal.
*   **COSO** proporciona el marco para organizar la respuesta a esos riesgos.
*   Los **Controles (ITGC, Procesos, SoD, Awareness)** son la respuesta práctica para mitigar los riesgos y prevenir que las **Amenazas** se materialicen.
*   **DevSecOps** construye defensas desde el inicio en el software.
*   El **BCP** y la **Respuesta a Incidentes** son los planes para actuar cuando los controles fallan.
*   La **Auditoría**, guiada por sus **Normas**, evalúa la eficacia de todo el sistema, proporcionando retroalimentación para la mejora continua.

Comprender la interacción entre estos elementos es fundamental para gestionar la seguridad y el control en el entorno digital actual.

---

## Información Adicional Integrada de la Transcripción

### Perspectivas Prácticas Adicionales

#### Sobre la Implementación de Awareness
La transcripción enfatiza que los programas de concienciación efectivos requieren **repetición y visibilidad constante**, no solo información pasiva. Los ejercicios de phishing simulado son fundamentales como "forma de prueba y aprendizaje" donde el objetivo es educar, no castigar.

#### Sobre la Separación de Ambientes
La transcripción destaca que la separación de ambientes (DEV/QA/PROD) puede ser **física o lógica**, pero es vital para prevenir que los desarrolladores trabajen directamente en producción y asegurar que todos los cambios pasen por un proceso de pruebas y aprobación antes de impactar al negocio.

#### Sobre la Gestión de Accesos Privilegiados
Se enfatiza que **casi todos los componentes de TI tienen cuentas administrativas privilegiadas** y que debido al potencial de abuso o error, se necesitan medidas de seguridad adicionales. La evolución desde "password vaulting" tradicional hacia software especializado de gestión de acceso privilegiado (PAM) refleja la maduración de las prácticas de seguridad.

#### Sobre la Evolución de Metodologías
La transcripción ilustra claramente la evolución desde metodologías waterfall tradicionales (donde la seguridad ocurre tarde y es costosa de corregir) hacia enfoques ágiles y DevSecOps, donde la seguridad se integra desde el inicio como una responsabilidad compartida.

### Conexiones Clave Reforzadas

1. **El Factor Humano como Vector Crítico:** La transcripción refuerza que sin importar cuántas defensas tecnológicas se implementen, el factor humano sigue siendo el vector de ataque inicial más exitoso.

2. **La Interconexión de Controles:** Se evidencia cómo los ITGC no son elementos aislados sino que forman un ecosistema integrado donde la falla en un dominio puede comprometer otros.

3. **La Importancia del Contexto Organizacional:** El gobierno de TI no es solo gestión técnica, sino alineación estratégica que debe considerar todos los stakeholders relevantes.

4. **La Evolución Continua:** Los marcos y prácticas evolucionan constantemente (como COSO 2013, COSO ERM 2017) para adaptarse a entornos de negocio más complejos con avances tecnológicos significativos.

### Reflexión Final

La transcripción proporciona una perspectiva práctica y detallada que complementa perfectamente el marco teórico del resumen original. Destaca que la seguridad y auditoría en el entorno digital actual no son solo cuestiones técnicas, sino que requieren un enfoque holístico que integre personas, procesos y tecnología en un ecosistema dinámico y continuo de protección organizacional.